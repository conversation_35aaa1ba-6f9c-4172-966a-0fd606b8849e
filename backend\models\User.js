// models/User.js
const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  name: String,
  email: { type: String, unique: true },
  password: String,
  role: { type: String, enum: ['donor', 'hospital', 'admin'], default: 'donor' },
  bloodType: { type: String },
  location: { type: String },
  lastDonation: Date,
  phone: String,
  // Admin-related fields
  status: { type: String, enum: ['active', 'banned', 'inactive', 'rejected'], default: 'active' },
  verified: { type: Boolean, default: true },
  // Ban-related fields
  banReason: String,
  bannedAt: Date,
  bannedBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  // Hospital-specific fields
  address: String,
  documents: { type: String, enum: ['pending', 'complete', 'incomplete'], default: 'complete' },
  // Activity tracking
  lastLogin: Date
}, { timestamps: true });

module.exports = mongoose.model('User', userSchema);
