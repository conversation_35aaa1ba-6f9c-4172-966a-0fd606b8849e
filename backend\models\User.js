// models/User.js
const mongoose = require('mongoose');

const userSchema = new mongoose.Schema({
  name: String,
  email: { type: String, unique: true },
  password: String,
  role: { type: String, enum: ['donor', 'admin'], default: 'donor' },
  bloodType: { type: String },
  location: { type: String },
  lastDonation: Date,
  phone: String,
}, { timestamps: true });

module.exports = mongoose.model('User', userSchema);
