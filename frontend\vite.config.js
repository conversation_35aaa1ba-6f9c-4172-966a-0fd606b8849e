import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import tailwindcss from '@tailwindcss/vite'
// https://vite.dev/config/
export default defineConfig({
  plugins: [
    tailwindcss(),
    react()
  ],
  server: {
    port: 5174, // Frontend on 5174
    proxy: {
      '/api': {
        target: 'http://localhost:3001', // Backend on 3001
        changeOrigin: true,
        secure: false,
      }
    }
  }
})
