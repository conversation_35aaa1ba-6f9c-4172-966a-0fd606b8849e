const User = require('../models/User');
const bcrypt = require('bcryptjs');
const { generateToken } = require('../utils/jwt');

exports.registerDonor = async (req, res) => {
  try {
    const { name, email, password, bloodType, location, phone } = req.body;

    const existing = await User.findOne({ email });
    if (existing) return res.status(400).json({ success: false, message: "Email already in use" });

    const hash = await bcrypt.hash(password, 10);
    const user = await User.create({ name, email, password: hash, bloodType, location, phone });

    res.status(201).json({ success: true, token: generateToken(user) });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};

exports.registerHospital = async (req, res) => {
  try {
    const { name, email, password, address, phone, location } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ success: false, message: 'Email already in use' });
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create hospital user
    const hospital = new User({
      name,
      email,
      password: hashedPassword,
      role: 'hospital',
      location,
      phone,
      address,
      status: 'active',
      verified: false, // Hospitals need verification
      documents: 'pending'
    });

    await hospital.save();

    res.status(201).json({
      success: true,
      message: 'Hospital registered successfully. Awaiting verification.',
      token: generateToken(hospital),
      user: {
        id: hospital._id,
        name: hospital.name,
        email: hospital.email,
        role: hospital.role,
        verified: hospital.verified
      }
    });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};

// Register Admin - Only existing admins can create new admins
exports.registerAdmin = async (req, res) => {
  try {
    const { name, email, password, location, phone } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      return res.status(400).json({ success: false, message: 'User already exists' });
    }

    // Hash password
    const salt = await bcrypt.genSalt(10);
    const hashedPassword = await bcrypt.hash(password, salt);

    // Create admin user
    const admin = new User({
      name,
      email,
      password: hashedPassword,
      role: 'admin',
      location,
      phone,
      status: 'active',
      verified: true
    });

    await admin.save();

    res.status(201).json({
      success: true,
      message: 'Admin user created successfully',
      token: generateToken(admin),
      user: {
        id: admin._id,
        name: admin.name,
        email: admin.email,
        role: admin.role
      }
    });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};

exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;

    const user = await User.findOne({ email });
    if (!user) return res.status(404).json({ success: false, message: 'User not found' });

    const match = await bcrypt.compare(password, user.password);
    if (!match) return res.status(401).json({ success: false, message: 'Incorrect password' });

    res.json({
      success: true,
      token: generateToken(user),
      user: {
        id: user._id,
        name: user.name,
        email: user.email,
        role: user.role // Use the actual role from database
      }
    });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};
