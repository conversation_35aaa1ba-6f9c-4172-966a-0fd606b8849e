const User = require('../models/User');
const Hospital = require('../models/Hospital');
const bcrypt = require('bcryptjs');
const { generateToken } = require('../utils/jwt');

exports.registerDonor = async (req, res) => {
  try {
    const { name, email, password, bloodType, location, phone } = req.body;

    const existing = await User.findOne({ email });
    if (existing) return res.status(400).json({ success: false, message: "Email already in use" });

    const hash = await bcrypt.hash(password, 10);
    const user = await User.create({ name, email, password: hash, bloodType, location, phone });

    res.status(201).json({ success: true, token: generateToken(user) });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};

exports.registerHospital = async (req, res) => {
  try {
    const { name, email, password, address, phone, location } = req.body;

    const existing = await Hospital.findOne({ email });
    if (existing) return res.status(400).json({ success: false, message: "Email already in use" });

    const hash = await bcrypt.hash(password, 10);
    const hospital = await Hospital.create({ name, email, password: hash, address, phone, location });

    res.status(201).json({ success: true, token: generateToken(hospital) });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};

exports.login = async (req, res) => {
  try {
    const { email, password } = req.body;

    const user = await User.findOne({ email }) || await Hospital.findOne({ email });
    if (!user) return res.status(404).json({ success: false, message: 'User not found' });

    const match = await bcrypt.compare(password, user.password);
    if (!match) return res.status(401).json({ success: false, message: 'Incorrect password' });

    res.json({ success: true, token: generateToken(user) });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};
