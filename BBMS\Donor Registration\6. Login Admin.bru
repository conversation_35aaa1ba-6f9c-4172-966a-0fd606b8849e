meta {
  name: 6. <PERSON><PERSON> (Admin)
  type: http
  seq: 6
}

post {
  url: http://localhost:3001/api/auth/login
  body: json
  auth: inherit
}

body:json {
  {
    "email": "<EMAIL>",
    "password": "admin123"
  }
}

tests {
  test("Should login admin successfully", function() {
    expect(res.getStatus()).to.equal(200);
    expect(res.getBody().success).to.equal(true);
    expect(res.getBody().token).to.be.a('string');
  });
  
  test("Should save admin token", function() {
    if (res.getBody().token) {
      bru.setVar("admin_token", res.getBody().token);
    }
  });
}

docs {
  Note: Make sure to manually update the user role to 'admin' in the database before testing admin endpoints
}
