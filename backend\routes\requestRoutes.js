const express = require('express');
const router = express.Router();
const requestController = require('../controllers/requestController');
const auth = require('../middleware/auth');

router.post('/create', auth(['hospital']), requestController.createRequest);
router.get('/nearby', auth(['donor']), requestController.getNearbyRequests);
router.patch('/fulfill/:id', auth(['hospital']), requestController.fulfillRequest);

module.exports = router;
