meta {
  name: 2. Invalid Login
  type: http
  seq: 2
}

post {
  url: http://localhost:3001/api/auth/login
  body: json
  auth: inherit
}

body:json {
  {
    "email": "<EMAIL>",
    "password": "wrongpassword"
  }
}

tests {
  test("Should reject invalid login", function() {
    expect(res.getStatus()).to.equal(404);
    expect(res.getBody().success).to.equal(false);
    expect(res.getBody().message).to.include("User not found");
  });
}
