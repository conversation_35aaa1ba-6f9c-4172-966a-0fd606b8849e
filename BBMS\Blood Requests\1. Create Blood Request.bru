meta {
  name: 1. Create Blood Request
  type: http
  seq: 1
}

post {
  url: http://localhost:3001/api/requests/create
  body: json
  auth: bearer
}

headers {
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************.9--nWQiVp_adjri2wkHVvryrnP9985npOrFzCTN8M5Y
}

auth:bearer {
  token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.********************************************************************************************************************************************************************************************************************************************************************.9--nWQiVp_adjri2wkHVvryrnP9985npOrFzCTN8M5Y
}

body:json {
  {
    "bloodType": "A+",
    "units": 2,
    "urgency": "high",
    "description": "Cancer patient requires A+ blood for treatment",
    "deadline": "2024-12-31T23:59:59.000Z"
  }
}

tests {
  test("Should create blood request successfully", function() {
    expect(res.getStatus()).to.equal(201);
    expect(res.getBody().success).to.equal(true);
    expect(res.getBody().request).to.be.an('object');
    expect(res.getBody().request.bloodType).to.equal("O+");
    expect(res.getBody().request.units).to.equal(5);
    expect(res.getBody().request.urgency).to.equal("high");
  });
  
  test("Should save request ID", function() {
    if (res.getBody().request && res.getBody().request._id) {
      bru.setVar("request_id", res.getBody().request._id);
    }
  });
}
