meta {
  name: 1. Create Blood Request
  type: http
  seq: 1
}

post {
  url: http://localhost:3001/api/requests/create
  body: json
  auth: bearer
}

headers {
  Authentication: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4MzE3ZjI4MGM1NmYwZWYwOWNhZmVlYyIsIm5hbWUiOiJEYWxtYXIgU3BlY2lhbGlzdCBIb3NwaXRhbCIsImlhdCI6MTc0ODA3NTAwMCwiZXhwIjoxNzQ4Njc5ODAwfQ.sfkYKZQiRWH7IWYCaOZqkGrqJAkSDTGiHxrl7c82z5s
}

auth:bearer {
  token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpZCI6IjY4MzE3ZjI4MGM1NmYwZWYwOWNhZmVlYyIsIm5hbWUiOiJEYWxtYXIgU3BlY2lhbGlzdCBIb3NwaXRhbCIsImlhdCI6MTc0ODA3NTAwMCwiZXhwIjoxNzQ4Njc5ODAwfQ.sfkYKZQiRWH7IWYCaOZqkGrqJAkSDTGiHxrl7c82z5s
}

body:json {
  {
    "bloodType": "O+",
    "units": 5,
    "urgency": "high"
  }
}

tests {
  test("Should create blood request successfully", function() {
    expect(res.getStatus()).to.equal(201);
    expect(res.getBody().success).to.equal(true);
    expect(res.getBody().request).to.be.an('object');
    expect(res.getBody().request.bloodType).to.equal("O+");
    expect(res.getBody().request.units).to.equal(5);
    expect(res.getBody().request.urgency).to.equal("high");
  });
  
  test("Should save request ID", function() {
    if (res.getBody().request && res.getBody().request._id) {
      bru.setVar("request_id", res.getBody().request._id);
    }
  });
}
