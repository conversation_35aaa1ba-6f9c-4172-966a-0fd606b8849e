meta {
  name: 4. Get All Hospitals
  type: http
  seq: 4
}

get {
  url: http://localhost:3001/api/admin/hospitals
  body: none
  auth: bearer
}

headers {
  Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************.AmdvQlqQDFBEBx-nagRjcKW_9j5RcASd54MzHw5mbcU
}

auth:bearer {
  token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************.AmdvQlqQDFBEBx-nagRjcKW_9j5RcASd54MzHw5mbcU
}

tests {
  test("Should get all hospitals successfully", function() {
    expect(res.getStatus()).to.equal(200);
    expect(res.getBody().hospitals).to.be.an('array');
    expect(res.getBody().stats).to.be.an('object');
  });
  
  test("Should return hospital statistics", function() {
    expect(res.getBody().stats.total).to.be.a('number');
    expect(res.getBody().stats.pending).to.be.a('number');
    expect(res.getBody().stats.verified).to.be.a('number');
  });
  
  test("Should return pending hospitals for verification", function() {
    expect(res.getBody().pendingHospitals).to.be.an('array');
  });
}
