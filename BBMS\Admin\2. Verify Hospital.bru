meta {
  name: 2. Verify Hospital
  type: http
  seq: 2
}

patch {
  url: http://localhost:5000/api/admin/verify-hospital/{{hospital_id}}
  body: none
  auth: bearer
}

auth:bearer {
  token: {{admin_token}}
}

tests {
  test("Should verify hospital successfully", function() {
    expect(res.getStatus()).to.equal(200);
    expect(res.getBody().success).to.equal(true);
    expect(res.getBody().hospital).to.be.an('object');
    expect(res.getBody().hospital.verified).to.equal(true);
  });
}

docs {
  Note: You need to get the hospital_id from the database first. 
  You can find it by checking the MongoDB collection or by creating a "Get All Hospitals" endpoint.
}
