const jwt = require('jsonwebtoken');
const secret = process.env.JWT_SECRET;

const generateToken = (user) => {
  return jwt.sign(
    {
      id: user._id,
      role: user.role,
      name: user.name || user.email,
      email: user.email,
      bloodType: user.bloodType,
      location: user.location,
      phone: user.phone,
      lastDonation: user.lastDonation
    },
    secret,
    { expiresIn: '7d' }
  );
};

module.exports = { generateToken };
