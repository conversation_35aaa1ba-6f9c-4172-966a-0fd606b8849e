meta {
  name: 3. Fulfill Request
  type: http
  seq: 3
}

patch {
  url: http://localhost:3001/api/requests/fulfill/{{request_id}}
  body: none
  auth: bearer
}

auth:bearer {
  token: {{hospital_token}}
}

tests {
  test("Should fulfill request successfully", function() {
    expect(res.getStatus()).to.equal(200);
    expect(res.getBody().success).to.equal(true);
    expect(res.getBody().request).to.be.an('object');
    expect(res.getBody().request.status).to.equal("fulfilled");
  });
}
