meta {
  name: 1. Get All Users
  type: http
  seq: 1
}

get {
  url: http://localhost:3001/api/admin/users
  body: none
  auth: bearer
}

auth:bearer {
  token: {{admin_token}}
}

tests {
  test("Should get all users successfully", function() {
    expect(res.getStatus()).to.equal(200);
    expect(res.getBody().success).to.equal(true);
    expect(res.getBody().users).to.be.an('array');
  });
  
  test("Should return user details", function() {
    const users = res.getBody().users;
    if (users.length > 0) {
      expect(users[0]).to.have.property('name');
      expect(users[0]).to.have.property('email');
      expect(users[0]).to.have.property('bloodType');
    }
  });
}
