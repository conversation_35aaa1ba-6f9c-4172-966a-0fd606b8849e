import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { 
  Heart, 
  MapPin, 
  Calendar, 
  Clock, 
  AlertCircle, 
  Award,
  Phone,
  User,
  Activity,
  Target,
  Star,
  Gift,
  Navigation,
  Bell,
  TrendingUp
} from 'lucide-react';

const DonorDashboard = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const [nearbyRequests, setNearbyRequests] = useState([]);
  const [donationHistory, setDonationHistory] = useState([]);
  const [loading, setLoading] = useState(true);
  const [nextEligibleDate, setNextEligibleDate] = useState(null);

  // Mock donor profile data (will be replaced with real API data)
  const donorProfile = {
    name: user?.name || '<PERSON>',
    bloodType: 'O+', // This should come from user profile
    location: 'New York, NY',
    phone: '+****************',
    lastDonation: '2024-01-15',
    totalDonations: 5,
    livesSaved: 15,
    achievements: ['First Donation', 'Life Saver', 'Regular Donor']
  };

  // Calculate next eligible donation date
  useEffect(() => {
    if (donorProfile.lastDonation) {
      const lastDate = new Date(donorProfile.lastDonation);
      const nextDate = new Date(lastDate);
      nextDate.setDate(lastDate.getDate() + 56); // 8 weeks between donations
      
      const today = new Date();
      const daysUntilEligible = Math.ceil((nextDate - today) / (1000 * 60 * 60 * 24));
      
      setNextEligibleDate({
        date: nextDate,
        daysRemaining: daysUntilEligible > 0 ? daysUntilEligible : 0,
        canDonate: daysUntilEligible <= 0
      });
    }
  }, [donorProfile.lastDonation]);

  // Fetch nearby blood requests
  useEffect(() => {
    const fetchNearbyRequests = async () => {
      try {
        const response = await axios.get(`/api/requests/nearby?bloodType=${donorProfile.bloodType}&location=${donorProfile.location}`);
        if (response.data.success) {
          setNearbyRequests(response.data.requests || []);
        }
      } catch (error) {
        console.error('Error fetching nearby requests:', error);
        // Use mock data for now
        setNearbyRequests([
          {
            id: 1,
            hospitalName: 'Kalkaal Hospital',
            bloodType: 'O+',
            units: 3,
            urgency: 'high',
            location: 'Mogadishu',
            distance: '2.5 km',
            timePosted: '2 hours ago'
          },
          {
            id: 2,
            hospitalName: 'Medina Hospital',
            bloodType: 'O+',
            units: 2,
            urgency: 'medium',
            location: 'Hodan District',
            distance: '5.1 km',
            timePosted: '4 hours ago'
          }
        ]);
      } finally {
        setLoading(false);
      }
    };

    fetchNearbyRequests();
  }, [donorProfile.bloodType, donorProfile.location]);

  // Mock donation history
  useEffect(() => {
    setDonationHistory([
      {
        id: 1,
        date: '2024-01-15',
        hospital: 'City General Hospital',
        status: 'completed',
        units: 1,
        bloodType: 'O+'
      },
      {
        id: 2,
        date: '2023-11-20',
        hospital: 'Metro Medical Center',
        status: 'completed',
        units: 1,
        bloodType: 'O+'
      },
      {
        id: 3,
        date: '2023-09-10',
        hospital: 'Community Hospital',
        status: 'completed',
        units: 1,
        bloodType: 'O+'
      }
    ]);
  }, []);

  const getUrgencyColor = (urgency) => {
    switch (urgency) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleDonateNow = () => {
    navigate('/blood-requests');
  };

  const handleBookAppointment = () => {
    // TODO: Implement appointment booking
    alert('Appointment booking feature coming soon!');
  };

  const handleRespondToRequest = (requestId) => {
    // TODO: Implement request response
    alert(`Responding to request ${requestId}. Feature coming soon!`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Welcome Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Heart className="h-8 w-8 text-red-500 fill-current blood-drop" />
            <h1 className="text-3xl font-bold text-gray-800">
              Welcome back, {donorProfile.name}! 🩸
            </h1>
          </div>
          <p className="text-gray-600 text-lg">
            Thank you for being a life-saving donor. Your {donorProfile.bloodType} blood has helped save {donorProfile.livesSaved} lives!
          </p>
        </div>

        {/* Donor Profile Card */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
          <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center space-x-2">
            <User className="h-5 w-5" />
            <span>Your Profile</span>
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="flex items-center space-x-3">
              <Heart className="h-5 w-5 text-red-500" />
              <div>
                <p className="text-sm text-gray-600">Blood Type</p>
                <p className="font-bold text-lg" style={{ color: '#4189DD' }}>{donorProfile.bloodType}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <MapPin className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm text-gray-600">Location</p>
                <p className="font-semibold text-gray-800">{donorProfile.location}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Phone className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm text-gray-600">Phone</p>
                <p className="font-semibold text-gray-800">{donorProfile.phone}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Calendar className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm text-gray-600">Last Donation</p>
                <p className="font-semibold text-gray-800">
                  {new Date(donorProfile.lastDonation).toLocaleDateString()}
                </p>
              </div>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-2 space-y-8">
            {/* Next Eligible Date */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-4 flex items-center space-x-2">
                <Clock className="h-5 w-5" />
                <span>Next Donation Eligibility</span>
              </h2>
              {nextEligibleDate && (
                <div className="text-center">
                  {nextEligibleDate.canDonate ? (
                    <div className="bg-green-50 border border-green-200 rounded-lg p-6">
                      <div className="text-green-600 mb-4">
                        <Heart className="h-12 w-12 mx-auto blood-drop" />
                      </div>
                      <h3 className="text-2xl font-bold text-green-800 mb-2">You Can Donate Now! 🎉</h3>
                      <p className="text-green-700 mb-4">You're eligible to make your next life-saving donation.</p>
                      <div className="flex space-x-4 justify-center">
                        <button 
                          onClick={handleDonateNow}
                          className="btn-primary flex items-center space-x-2"
                        >
                          <Heart className="h-5 w-5" />
                          <span>Donate Now</span>
                        </button>
                        <button 
                          onClick={handleBookAppointment}
                          className="px-6 py-3 border-2 border-blue-600 text-blue-600 rounded-lg font-semibold hover:bg-blue-50 transition-colors"
                        >
                          Book Appointment
                        </button>
                      </div>
                    </div>
                  ) : (
                    <div className="bg-blue-50 border border-blue-200 rounded-lg p-6">
                      <div className="text-blue-600 mb-4">
                        <Calendar className="h-12 w-12 mx-auto" />
                      </div>
                      <h3 className="text-2xl font-bold text-blue-800 mb-2">
                        You can donate again in {nextEligibleDate.daysRemaining} days
                      </h3>
                      <p className="text-blue-700 mb-2">
                        Next eligible date: {nextEligibleDate.date.toLocaleDateString()}
                      </p>
                      <p className="text-sm text-blue-600">
                        We'll notify you when you're eligible to donate again!
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>

            {/* Nearby Requests - "Who Needs Me?" */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-800 flex items-center space-x-2">
                  <Navigation className="h-5 w-5" />
                  <span>Who Needs Me? ({donorProfile.bloodType})</span>
                </h2>
                <button 
                  onClick={() => navigate('/blood-requests')}
                  className="text-blue-600 hover:text-blue-800 font-medium text-sm"
                >
                  View All Requests
                </button>
              </div>
              
              {nearbyRequests.length > 0 ? (
                <div className="space-y-4">
                  {nearbyRequests.slice(0, 3).map((request) => (
                    <div key={request.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex justify-between items-start mb-3">
                        <div>
                          <h3 className="font-bold text-gray-800 flex items-center space-x-2">
                            <Heart className="h-4 w-4 text-red-500 blood-drop" />
                            <span>{request.hospitalName}</span>
                          </h3>
                          <p className="text-sm text-gray-600 flex items-center space-x-1 mt-1">
                            <MapPin className="h-3 w-3" />
                            <span>{request.location} • {request.distance}</span>
                          </p>
                        </div>
                        <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getUrgencyColor(request.urgency)}`}>
                          {request.urgency} priority
                        </span>
                      </div>
                      
                      <div className="flex justify-between items-center">
                        <div className="text-sm text-gray-600">
                          <span className="font-semibold">{request.units} units needed</span> • Posted {request.timePosted}
                        </div>
                        <button 
                          onClick={() => handleRespondToRequest(request.id)}
                          className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors text-sm font-medium"
                        >
                          Respond
                        </button>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Heart className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-600">No urgent requests for {donorProfile.bloodType} in your area right now.</p>
                  <p className="text-sm text-gray-500 mt-2">We'll notify you when hospitals need your blood type!</p>
                </div>
              )}
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Stats */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>Your Impact</span>
              </h3>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Total Donations</span>
                  <span className="font-bold text-2xl text-red-500">{donorProfile.totalDonations}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Lives Saved</span>
                  <span className="font-bold text-2xl text-green-500">{donorProfile.livesSaved}</span>
                </div>
                <div className="flex justify-between items-center">
                  <span className="text-gray-600">Blood Type</span>
                  <span className="font-bold text-xl" style={{ color: '#4189DD' }}>{donorProfile.bloodType}</span>
                </div>
              </div>
            </div>

            {/* Achievements */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center space-x-2">
                <Award className="h-5 w-5" />
                <span>Achievements</span>
              </h3>
              <div className="space-y-3">
                {donorProfile.achievements.map((achievement, index) => (
                  <div key={index} className="flex items-center space-x-3 p-3 bg-yellow-50 border border-yellow-200 rounded-lg">
                    <Star className="h-5 w-5 text-yellow-500" />
                    <span className="font-medium text-yellow-800">{achievement}</span>
                  </div>
                ))}
              </div>
            </div>

            {/* Emergency Alert */}
            <div className="bg-red-50 border border-red-200 rounded-xl p-6">
              <div className="flex items-center space-x-3 mb-3">
                <AlertCircle className="h-6 w-6 text-red-500 blood-drop" />
                <h3 className="font-bold text-red-800">Urgent Alert!</h3>
              </div>
              <p className="text-red-700 text-sm mb-4">
                Critical shortage of {donorProfile.bloodType} blood at Kalkaal Hospital. 3 patients need immediate help!
              </p>
              <button 
                onClick={() => handleRespondToRequest('emergency')}
                className="w-full bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors font-medium"
              >
                Respond to Emergency
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default DonorDashboard;
