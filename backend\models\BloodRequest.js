// models/BloodRequest.js
const mongoose = require('mongoose');

const requestSchema = new mongoose.Schema({
  hospitalId: { type: mongoose.Schema.Types.ObjectId, ref: 'Hospital' },
  bloodType: String,
  units: Number,
  urgency: { type: String, enum: ['low', 'medium', 'high'] },
  status: { type: String, enum: ['pending', 'fulfilled'], default: 'pending' },
  matchedDonors: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
}, { timestamps: true });

module.exports = mongoose.model('BloodRequest', requestSchema);
