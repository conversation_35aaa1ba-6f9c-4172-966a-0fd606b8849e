// models/BloodRequest.js
const mongoose = require('mongoose');

const requestSchema = new mongoose.Schema({
  hospital: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
  bloodType: {
    type: String,
    enum: ['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'],
    required: true
  },
  units: { type: Number, required: true, min: 1 },
  urgency: { type: String, enum: ['low', 'medium', 'high'], default: 'medium' },
  status: { type: String, enum: ['pending', 'fulfilled', 'cancelled', 'expired'], default: 'pending' },
  description: { type: String, required: true },
  location: String,
  deadline: Date,
  matchedDonors: [{ type: mongoose.Schema.Types.ObjectId, ref: 'User' }],
  fulfilledBy: { type: mongoose.Schema.Types.ObjectId, ref: 'User' },
  fulfilledAt: Date,
  notes: String
}, { timestamps: true });

module.exports = mongoose.model('BloodRequest', requestSchema);
