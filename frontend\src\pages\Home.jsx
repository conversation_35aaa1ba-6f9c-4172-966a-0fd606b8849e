import { Link } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { 
  Heart, 
  Users, 
  Activity, 
  Award,
  ArrowRight,
  CheckCircle,
  Clock,
  MapPin
} from 'lucide-react';

const Home = () => {
  const { isAuthenticated } = useAuth();

  const stats = [
    { icon: Users, label: 'Registered Donors', value: '2,500+', color: 'text-blue-600' },
    { icon: Heart, label: 'Lives Saved', value: '1,200+', color: 'text-red-500' },
    { icon: Activity, label: 'Blood Requests', value: '450+', color: 'text-green-500' },
    { icon: Award, label: 'Partner Hospitals', value: '25+', color: 'text-purple-500' }
  ];

  const features = [
    {
      icon: Clock,
      title: 'Quick Response',
      description: 'Get matched with nearby donors within minutes of posting a request.'
    },
    {
      icon: MapPin,
      title: 'Location-Based',
      description: 'Find donors and requests in your area for faster blood delivery.'
    },
    {
      icon: CheckCircle,
      title: 'Verified Users',
      description: 'All donors and hospitals are verified for safety and reliability.'
    }
  ];

  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="relative bg-gradient-to-br from-blue-50 to-white py-20">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            <div>
              <h1 className="text-4xl md:text-6xl font-bold text-gray-800 mb-6">
                Save Lives with{' '}
                <span className="blood-drop" style={{ color: '#4189DD' }}>
                  🩸 SomDonate
                </span>
              </h1>
              <p className="text-xl text-gray-600 mb-8 leading-relaxed">
                Connect blood donors with hospitals in need. Every donation counts, 
                and together we can make a difference in our community.
              </p>
              <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
                {!isAuthenticated ? (
                  <>
                    <Link 
                      to="/register" 
                      className="btn-primary flex items-center justify-center space-x-2"
                    >
                      <Heart className="h-5 w-5" />
                      <span>Become a Donor</span>
                      <ArrowRight className="h-4 w-4" />
                    </Link>
                    <Link 
                      to="/login" 
                      className="px-6 py-3 border-2 border-blue-600 text-blue-600 rounded-lg font-semibold hover:bg-blue-50 transition-colors flex items-center justify-center space-x-2"
                    >
                      <span>Hospital Login</span>
                    </Link>
                  </>
                ) : (
                  <Link 
                    to="/dashboard" 
                    className="btn-primary flex items-center justify-center space-x-2"
                  >
                    <Activity className="h-5 w-5" />
                    <span>Go to Dashboard</span>
                    <ArrowRight className="h-4 w-4" />
                  </Link>
                )}
              </div>
            </div>
            
            <div className="relative">
              <div className="bg-white rounded-2xl shadow-2xl p-8 transform rotate-3 hover:rotate-0 transition-transform duration-300">
                <div className="text-center">
                  <Heart className="h-24 w-24 text-red-500 fill-current mx-auto mb-6 blood-drop" />
                  <h3 className="text-2xl font-bold text-gray-800 mb-4">
                    One Donation = Three Lives Saved
                  </h3>
                  <p className="text-gray-600">
                    Your single blood donation can be separated into red cells, 
                    platelets, and plasma to help multiple patients.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Stats Section */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {stats.map((stat, index) => (
              <div key={index} className="text-center p-6 rounded-lg bg-gray-50 hover:shadow-lg transition-shadow">
                <stat.icon className={`h-12 w-12 ${stat.color} mx-auto mb-4`} />
                <div className="text-3xl font-bold text-gray-800 mb-2">{stat.value}</div>
                <div className="text-gray-600">{stat.label}</div>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl md:text-4xl font-bold text-gray-800 mb-4">
              Why Choose SomDonate?
            </h2>
            <p className="text-xl text-gray-600 max-w-3xl mx-auto">
              Our platform makes blood donation simple, safe, and efficient for everyone involved.
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {features.map((feature, index) => (
              <div key={index} className="bg-white p-8 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
                <feature.icon className="h-12 w-12 text-blue-600 mb-6" />
                <h3 className="text-xl font-bold text-gray-800 mb-4">{feature.title}</h3>
                <p className="text-gray-600">{feature.description}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-16 bg-gradient-to-r from-blue-600 to-blue-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-6">
            Ready to Save Lives?
          </h2>
          <p className="text-xl text-blue-100 mb-8 max-w-2xl mx-auto">
            Join thousands of donors and hospitals already using SomDonate to make a difference.
          </p>
          {!isAuthenticated && (
            <div className="flex flex-col sm:flex-row justify-center space-y-4 sm:space-y-0 sm:space-x-4">
              <Link 
                to="/register" 
                className="bg-white text-blue-600 px-8 py-3 rounded-lg font-semibold hover:bg-gray-100 transition-colors flex items-center justify-center space-x-2"
              >
                <Heart className="h-5 w-5" />
                <span>Register as Donor</span>
              </Link>
              <Link 
                to="/register" 
                className="bg-transparent border-2 border-white text-white px-8 py-3 rounded-lg font-semibold hover:bg-white hover:text-blue-600 transition-colors"
              >
                Register as Hospital
              </Link>
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default Home;
