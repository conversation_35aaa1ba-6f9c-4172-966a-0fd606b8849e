const mongoose = require('mongoose');
const User = require('../models/User');
require('dotenv').config();

const updateToAdmin = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/bloodbank');
    console.log('Connected to MongoDB');

    // Find user by email and update role to admin
    const user = await User.findOneAndUpdate(
      { email: '<EMAIL>' },
      { 
        role: 'admin',
        status: 'active',
        verified: true 
      },
      { new: true }
    );

    if (user) {
      console.log('✅ User updated to admin successfully!');
      console.log('Admin Details:');
      console.log('Name:', user.name);
      console.log('Email:', user.email);
      console.log('Role:', user.role);
      console.log('Status:', user.status);
      console.log('\n🎯 Now you can:');
      console.log('1. <NAME_EMAIL> / admin123');
      console.log('2. Create more admins using POST /api/auth/register-admin');
      console.log('3. No more hardcoded admin emails!');
    } else {
      console.log('❌ User not found. <NAME_EMAIL> first.');
    }

  } catch (error) {
    console.error('Error updating user:', error);
  } finally {
    mongoose.connection.close();
  }
};

updateToAdmin();
