meta {
  name: GetUsersFFilter
  type: http
  seq: 6
}

get {
  url: http://localhost:3001/api/admin/users?role=donor&page=1&limit=10&search=ahmed
  body: none
  auth: bearer
}

params:query {
  role: donor
  page: 1
  limit: 10
  search: ahmed
}

auth:bearer {
  token: eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.**************************************************************************************************************************************************************************************************************************************************************.AmdvQlqQDFBEBx-nagRjcKW_9j5RcASd54MzHw5mbcU
}
