const jwt = require('jsonwebtoken');
const secret = process.env.JWT_SECRET;

const auth = (roles = []) => {
  return (req, res, next) => {
    const token = req.headers.authorization?.split(" ")[1];
    if (!token) return res.status(401).json({ success: false, message: "No token provided" });

    try {
      const decoded = jwt.verify(token, secret);
      if (roles.length && !roles.includes(decoded.role)) {
        return res.status(403).json({ success: false, message: "Access denied" });
      }
      req.user = decoded;
      next();
    } catch (err) {
      return res.status(401).json({ success: false, message: "Invalid token" });
    }
  };
};

module.exports = auth;
