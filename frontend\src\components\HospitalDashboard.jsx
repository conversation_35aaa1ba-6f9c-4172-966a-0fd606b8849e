import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';
import {
  Building,
  Heart,
  Users,
  Activity,
  Plus,
  Clock,
  CheckCircle,
  AlertCircle,
  MapPin,
  Phone,
  Mail,
  Edit,
  Send,
  Calendar,
  TrendingUp,
  Shield,
  Target,
  Bell
} from 'lucide-react';

const HospitalDashboard = () => {
  const { user } = useAuth();
  const [activeRequests, setActiveRequests] = useState([]);
  const [matchedDonors, setMatchedDonors] = useState([]);
  const [hospitalStats, setHospitalStats] = useState({
    activeRequests: 0,
    matchedDonors: 0,
    fulfilledRequests: 0,
    avgResponseTime: '0h'
  });
  const [showCreateForm, setShowCreateForm] = useState(false);
  const [loading, setLoading] = useState(true);

  // Real hospital profile data from JWT
  const [hospitalProfile, setHospitalProfile] = useState({
    name: user?.name || '',
    email: '',
    location: '',
    phone: '',
    address: '',
    verified: false
  });

  // Fetch real hospital profile data
  useEffect(() => {
    const fetchHospitalProfile = async () => {
      try {
        if (user) {
          const token = localStorage.getItem('token');
          if (token) {
            try {
              const payload = JSON.parse(atob(token.split('.')[1]));
              console.log('Hospital JWT Payload:', payload);

              setHospitalProfile(prev => ({
                ...prev,
                name: payload.name || user.name,
                email: payload.email,
                location: payload.location || 'Not specified',
                phone: payload.phone || 'Not specified',
                address: payload.address || 'Not specified',
                verified: true // TODO: Get from backend verification status
              }));
            } catch (error) {
              console.error('Error decoding JWT:', error);
            }
          }
        }
      } catch (error) {
        console.error('Error fetching hospital profile:', error);
      }
    };

    fetchHospitalProfile();
  }, [user]);

  // Fetch hospital's blood requests
  useEffect(() => {
    const fetchHospitalRequests = async () => {
      try {
        // TODO: Replace with real API endpoint
        // const response = await axios.get('/api/hospital/requests');

        // Mock data for now
        setActiveRequests([
          {
            id: 1,
            bloodType: 'O+',
            units: 3,
            urgency: 'high',
            status: 'pending',
            datePosted: '2024-01-20',
            matchedDonors: 5,
            description: 'Emergency surgery patient needs O+ blood'
          },
          {
            id: 2,
            bloodType: 'A-',
            units: 2,
            urgency: 'medium',
            status: 'pending',
            datePosted: '2024-01-19',
            matchedDonors: 3,
            description: 'Cancer patient requires A- blood for treatment'
          },
          {
            id: 3,
            bloodType: 'B+',
            units: 1,
            urgency: 'low',
            status: 'fulfilled',
            datePosted: '2024-01-18',
            matchedDonors: 2,
            description: 'Routine surgery preparation'
          }
        ]);

        // Update stats
        setHospitalStats({
          activeRequests: 2,
          matchedDonors: 8,
          fulfilledRequests: 28,
          avgResponseTime: '2.5h'
        });

      } catch (error) {
        console.error('Error fetching hospital requests:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchHospitalRequests();
  }, []);

  // Fetch matched donors for hospital
  useEffect(() => {
    const fetchMatchedDonors = async () => {
      try {
        // TODO: Replace with real API endpoint
        // const response = await axios.get('/api/hospital/matched-donors');

        // Mock data for now
        setMatchedDonors([
          {
            id: 1,
            name: 'Ahmed Hassan',
            bloodType: 'O+',
            location: 'Mogadishu',
            distance: '2.1 km',
            lastDonation: '2023-11-15',
            phone: '+252 61 234 5678',
            available: true
          },
          {
            id: 2,
            name: 'Fatima Ali',
            bloodType: 'A-',
            location: 'Hodan District',
            distance: '3.5 km',
            lastDonation: '2023-12-01',
            phone: '+252 61 345 6789',
            available: true
          },
          {
            id: 3,
            name: 'Mohamed Omar',
            bloodType: 'O+',
            location: 'Karaan',
            distance: '4.2 km',
            lastDonation: '2023-10-20',
            phone: '+252 61 456 7890',
            available: false
          }
        ]);
      } catch (error) {
        console.error('Error fetching matched donors:', error);
      }
    };

    fetchMatchedDonors();
  }, []);

  const getUrgencyColor = (urgency) => {
    switch (urgency) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'pending': return 'bg-yellow-100 text-yellow-800';
      case 'fulfilled': return 'bg-green-100 text-green-800';
      case 'cancelled': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const handleCreateRequest = () => {
    setShowCreateForm(true);
  };

  const handleFulfillRequest = (requestId) => {
    // TODO: Implement fulfill request API call
    setActiveRequests(prev =>
      prev.map(req =>
        req.id === requestId
          ? { ...req, status: 'fulfilled' }
          : req
      )
    );
    alert(`Request ${requestId} marked as fulfilled!`);
  };

  const handleContactDonor = (donorId, donorName) => {
    // TODO: Implement contact donor functionality
    alert(`Contacting ${donorName}... Feature coming soon!`);
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading hospital dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Welcome Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Building className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-800">
              Welcome, {hospitalProfile.name}! 🏥
            </h1>
          </div>
          <p className="text-gray-600 text-lg">
            Manage your blood requests and connect with donors to save lives.
          </p>
        </div>

        {/* Hospital Profile Card */}
        <div className="bg-white rounded-xl shadow-lg p-6 mb-8">
          <div className="flex justify-between items-start mb-4">
            <h2 className="text-xl font-bold text-gray-800 flex items-center space-x-2">
              <Building className="h-5 w-5" />
              <span>Hospital Profile</span>
            </h2>
            <div className="flex items-center space-x-2">
              {hospitalProfile.verified ? (
                <span className="bg-green-100 text-green-800 px-3 py-1 rounded-full text-sm font-medium flex items-center space-x-1">
                  <Shield className="h-4 w-4" />
                  <span>Verified</span>
                </span>
              ) : (
                <span className="bg-yellow-100 text-yellow-800 px-3 py-1 rounded-full text-sm font-medium">
                  Pending Verification
                </span>
              )}
              <button className="text-blue-600 hover:text-blue-800">
                <Edit className="h-5 w-5" />
              </button>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="flex items-center space-x-3">
              <Mail className="h-5 w-5 text-blue-500" />
              <div>
                <p className="text-sm text-gray-600">Email</p>
                <p className="font-semibold text-gray-800">{hospitalProfile.email}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <MapPin className="h-5 w-5 text-green-500" />
              <div>
                <p className="text-sm text-gray-600">Location</p>
                <p className="font-semibold text-gray-800">{hospitalProfile.location}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Phone className="h-5 w-5 text-purple-500" />
              <div>
                <p className="text-sm text-gray-600">Phone</p>
                <p className="font-semibold text-gray-800">{hospitalProfile.phone}</p>
              </div>
            </div>
            <div className="flex items-center space-x-3">
              <Building className="h-5 w-5 text-gray-500" />
              <div>
                <p className="text-sm text-gray-600">Address</p>
                <p className="font-semibold text-gray-800">{hospitalProfile.address}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
          <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Requests</p>
                <p className="text-2xl font-bold text-gray-800">{hospitalStats.activeRequests}</p>
                <p className="text-sm text-red-600">Urgent attention needed</p>
              </div>
              <Activity className="h-8 w-8 text-red-500" />
            </div>
          </div>
          <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Matched Donors</p>
                <p className="text-2xl font-bold text-gray-800">{hospitalStats.matchedDonors}</p>
                <p className="text-sm text-blue-600">Available to contact</p>
              </div>
              <Users className="h-8 w-8 text-blue-500" />
            </div>
          </div>
          <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Fulfilled Requests</p>
                <p className="text-2xl font-bold text-gray-800">{hospitalStats.fulfilledRequests}</p>
                <p className="text-sm text-green-600">Lives saved</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </div>
          <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Avg Response Time</p>
                <p className="text-2xl font-bold text-gray-800">{hospitalStats.avgResponseTime}</p>
                <p className="text-sm text-purple-600">Getting faster</p>
              </div>
              <Clock className="h-8 w-8 text-purple-500" />
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Main Content - Active Requests */}
          <div className="lg:col-span-2 space-y-8">
            {/* Active Blood Requests */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-800 flex items-center space-x-2">
                  <Activity className="h-5 w-5" />
                  <span>Active Blood Requests</span>
                </h2>
                <button
                  onClick={handleCreateRequest}
                  className="btn-primary flex items-center space-x-2"
                >
                  <Plus className="h-5 w-5" />
                  <span>Create Request</span>
                </button>
              </div>

              {activeRequests.length > 0 ? (
                <div className="space-y-4">
                  {activeRequests.filter(req => req.status === 'pending').map((request) => (
                    <div key={request.id} className="border border-gray-200 rounded-lg p-4 hover:shadow-md transition-shadow">
                      <div className="flex justify-between items-start mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="bg-red-100 p-2 rounded-full">
                            <Heart className="h-5 w-5 text-red-500" />
                          </div>
                          <div>
                            <h3 className="font-bold text-gray-800 text-lg">{request.bloodType}</h3>
                            <p className="text-sm text-gray-600">{request.units} units needed</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className={`px-3 py-1 rounded-full text-xs font-medium border ${getUrgencyColor(request.urgency)}`}>
                            {request.urgency} priority
                          </span>
                          <span className={`px-3 py-1 rounded-full text-xs font-medium ${getStatusColor(request.status)}`}>
                            {request.status}
                          </span>
                        </div>
                      </div>

                      <p className="text-gray-700 mb-3">{request.description}</p>

                      <div className="flex justify-between items-center">
                        <div className="flex items-center space-x-4 text-sm text-gray-600">
                          <span className="flex items-center space-x-1">
                            <Calendar className="h-4 w-4" />
                            <span>Posted: {new Date(request.datePosted).toLocaleDateString()}</span>
                          </span>
                          <span className="flex items-center space-x-1">
                            <Users className="h-4 w-4" />
                            <span>{request.matchedDonors} matched donors</span>
                          </span>
                        </div>
                        <div className="flex space-x-2">
                          <button
                            onClick={() => handleFulfillRequest(request.id)}
                            className="bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm font-medium"
                          >
                            Mark Fulfilled
                          </button>
                          <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm">
                            Edit
                          </button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-8">
                  <Heart className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-600">No active blood requests.</p>
                  <button
                    onClick={handleCreateRequest}
                    className="mt-4 btn-primary"
                  >
                    Create Your First Request
                  </button>
                </div>
              )}
            </div>

            {/* Request History */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>Request History & Stats</span>
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="text-center p-4 bg-gray-50 rounded-lg">
                  <p className="text-2xl font-bold text-gray-800">28</p>
                  <p className="text-sm text-gray-600">Total Requests</p>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <p className="text-2xl font-bold text-green-600">25</p>
                  <p className="text-sm text-gray-600">Fulfilled</p>
                </div>
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <p className="text-2xl font-bold text-blue-600">89%</p>
                  <p className="text-sm text-gray-600">Success Rate</p>
                </div>
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Matched Donors */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4 flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>Matched Donors</span>
              </h3>

              {matchedDonors.length > 0 ? (
                <div className="space-y-3">
                  {matchedDonors.slice(0, 3).map((donor) => (
                    <div key={donor.id} className="border border-gray-200 rounded-lg p-3">
                      <div className="flex justify-between items-start mb-2">
                        <div>
                          <h4 className="font-semibold text-gray-800">{donor.name}</h4>
                          <p className="text-sm text-gray-600 flex items-center space-x-1">
                            <Heart className="h-3 w-3 text-red-500" />
                            <span>{donor.bloodType}</span>
                          </p>
                        </div>
                        <span className={`px-2 py-1 rounded-full text-xs ${
                          donor.available ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'
                        }`}>
                          {donor.available ? 'Available' : 'Not Available'}
                        </span>
                      </div>

                      <div className="text-xs text-gray-500 mb-3">
                        <p className="flex items-center space-x-1">
                          <MapPin className="h-3 w-3" />
                          <span>{donor.location} • {donor.distance}</span>
                        </p>
                        <p>Last donation: {new Date(donor.lastDonation).toLocaleDateString()}</p>
                      </div>

                      <button
                        onClick={() => handleContactDonor(donor.id, donor.name)}
                        className="w-full bg-blue-600 text-white px-3 py-2 rounded-lg hover:bg-blue-700 transition-colors text-sm font-medium flex items-center justify-center space-x-1"
                        disabled={!donor.available}
                      >
                        <Send className="h-4 w-4" />
                        <span>Contact Donor</span>
                      </button>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-4">
                  <Users className="h-8 w-8 text-gray-300 mx-auto mb-2" />
                  <p className="text-gray-600 text-sm">No matched donors yet.</p>
                </div>
              )}
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h3 className="text-lg font-bold text-gray-800 mb-4">Quick Actions</h3>
              <div className="space-y-3">
                <button
                  onClick={handleCreateRequest}
                  className="w-full text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-3"
                >
                  <Plus className="h-5 w-5 text-red-500" />
                  <span>Create Blood Request</span>
                </button>
                <button className="w-full text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-3">
                  <Users className="h-5 w-5 text-blue-500" />
                  <span>Browse All Donors</span>
                </button>
                <button className="w-full text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-3">
                  <Bell className="h-5 w-5 text-yellow-500" />
                  <span>Send Emergency Alert</span>
                </button>
                <button className="w-full text-left p-3 border border-gray-200 rounded-lg hover:bg-gray-50 transition-colors flex items-center space-x-3">
                  <Edit className="h-5 w-5 text-purple-500" />
                  <span>Update Hospital Profile</span>
                </button>
              </div>
            </div>

            {/* Emergency Contact */}
            <div className="bg-red-50 border border-red-200 rounded-xl p-6">
              <div className="flex items-center space-x-3 mb-3">
                <AlertCircle className="h-6 w-6 text-red-500" />
                <h3 className="font-bold text-red-800">Emergency Support</h3>
              </div>
              <p className="text-red-700 text-sm mb-4">
                Need urgent blood? Contact our emergency hotline for immediate assistance.
              </p>
              <button className="w-full bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors font-medium">
                Call Emergency Hotline
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HospitalDashboard;
