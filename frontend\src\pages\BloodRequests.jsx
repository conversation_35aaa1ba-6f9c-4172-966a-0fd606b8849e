import { useState } from 'react';
import { useAuth } from '../contexts/AuthContext';
import { Heart, MapPin, Clock, AlertCircle, Plus } from 'lucide-react';

const BloodRequests = () => {
  const { isHospital, isDonor } = useAuth();
  const [activeTab, setActiveTab] = useState('all');

  const mockRequests = [
    {
      id: 1,
      bloodType: 'O+',
      units: 3,
      urgency: 'high',
      hospital: 'City General Hospital',
      location: 'New York',
      timePosted: '2 hours ago',
      status: 'pending'
    },
    {
      id: 2,
      bloodType: 'A-',
      units: 2,
      urgency: 'medium',
      hospital: 'Metro Medical Center',
      location: 'Brooklyn',
      timePosted: '5 hours ago',
      status: 'pending'
    },
    {
      id: 3,
      bloodType: 'B+',
      units: 1,
      urgency: 'low',
      hospital: 'Community Hospital',
      location: 'Queens',
      timePosted: '1 day ago',
      status: 'fulfilled'
    }
  ];

  const getUrgencyColor = (urgency) => {
    switch (urgency) {
      case 'high': return 'text-red-600 bg-red-100';
      case 'medium': return 'text-yellow-600 bg-yellow-100';
      case 'low': return 'text-green-600 bg-green-100';
      default: return 'text-gray-600 bg-gray-100';
    }
  };

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="flex justify-between items-center mb-8">
          <div>
            <h1 className="text-3xl font-bold text-gray-800">
              {isHospital ? 'My Blood Requests' : 'Available Blood Requests'}
            </h1>
            <p className="text-gray-600 mt-2">
              {isHospital 
                ? 'Manage your hospital\'s blood requests and track responses'
                : 'Find blood requests in your area and help save lives'
              }
            </p>
          </div>
          {isHospital && (
            <button className="btn-primary flex items-center space-x-2">
              <Plus className="h-5 w-5" />
              <span>Create Request</span>
            </button>
          )}
        </div>

        {/* Tabs */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {['all', 'pending', 'fulfilled'].map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm capitalize ${
                    activeTab === tab
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  {tab} Requests
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Requests Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {mockRequests
            .filter(request => activeTab === 'all' || request.status === activeTab)
            .map((request) => (
            <div key={request.id} className="bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow">
              {/* Header */}
              <div className="flex justify-between items-start mb-4">
                <div className="flex items-center space-x-3">
                  <div className="bg-red-100 p-2 rounded-full">
                    <Heart className="h-6 w-6 text-red-500" />
                  </div>
                  <div>
                    <h3 className="text-xl font-bold text-gray-800">{request.bloodType}</h3>
                    <p className="text-sm text-gray-600">{request.units} units needed</p>
                  </div>
                </div>
                <span className={`px-3 py-1 rounded-full text-xs font-medium ${getUrgencyColor(request.urgency)}`}>
                  {request.urgency} priority
                </span>
              </div>

              {/* Details */}
              <div className="space-y-3 mb-6">
                <div className="flex items-center space-x-2 text-gray-600">
                  <MapPin className="h-4 w-4" />
                  <span className="text-sm">{request.hospital}</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-600">
                  <MapPin className="h-4 w-4" />
                  <span className="text-sm">{request.location}</span>
                </div>
                <div className="flex items-center space-x-2 text-gray-600">
                  <Clock className="h-4 w-4" />
                  <span className="text-sm">Posted {request.timePosted}</span>
                </div>
              </div>

              {/* Actions */}
              <div className="flex space-x-3">
                {isDonor && request.status === 'pending' && (
                  <button className="flex-1 btn-primary text-sm">
                    Respond to Request
                  </button>
                )}
                {isHospital && (
                  <>
                    {request.status === 'pending' && (
                      <button className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors text-sm">
                        Mark Fulfilled
                      </button>
                    )}
                    <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm">
                      Edit
                    </button>
                  </>
                )}
                <button className="px-4 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors text-sm">
                  Details
                </button>
              </div>

              {/* Status Badge */}
              {request.status === 'fulfilled' && (
                <div className="mt-4 flex items-center space-x-2 text-green-600">
                  <AlertCircle className="h-4 w-4" />
                  <span className="text-sm font-medium">Request Fulfilled</span>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Empty State */}
        {mockRequests.filter(request => activeTab === 'all' || request.status === activeTab).length === 0 && (
          <div className="text-center py-12">
            <Heart className="h-16 w-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-800 mb-2">No requests found</h3>
            <p className="text-gray-600">
              {activeTab === 'all' 
                ? 'No blood requests available at the moment.'
                : `No ${activeTab} requests found.`
              }
            </p>
          </div>
        )}
      </div>
    </div>
  );
};

export default BloodRequests;
