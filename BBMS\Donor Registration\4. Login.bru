meta {
  name: 4. <PERSON><PERSON> (<PERSON>or)
  type: http
  seq: 4
}

post {
  url: http://localhost:3001/api/auth/login
  body: json
  auth: inherit
}

body:json {
  {
    "email": "<EMAIL>",
    "password": "suldan123"
  }
}

tests {
  test("Should login successfully", function() {
    expect(res.getStatus()).to.equal(200);
    expect(res.getBody().success).to.equal(true);
    expect(res.getBody().token).to.be.a('string');
  });
  
  test("Should save token for future requests", function() {
    if (res.getBody().token) {
      bru.setVar("donor_token", res.getBody().token);
    }
  });
}
