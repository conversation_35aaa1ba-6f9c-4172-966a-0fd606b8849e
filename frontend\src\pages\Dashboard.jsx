import { useAuth } from '../contexts/AuthContext';
import { 
  Heart, 
  Activity, 
  Users, 
  Calendar,
  TrendingUp,
  MapPin,
  Clock,
  AlertCircle
} from 'lucide-react';

const Dashboard = () => {
  const { user, isDonor, isHospital, isAdmin } = useAuth();

  const donorStats = [
    { icon: Heart, label: 'Total Donations', value: '5', color: 'text-red-500' },
    { icon: Calendar, label: 'Last Donation', value: '2 months ago', color: 'text-blue-500' },
    { icon: Activity, label: 'Blood Type', value: 'O+', color: 'text-green-500' },
    { icon: TrendingUp, label: 'Lives Saved', value: '15', color: 'text-purple-500' }
  ];

  const hospitalStats = [
    { icon: Activity, label: 'Active Requests', value: '3', color: 'text-red-500' },
    { icon: Users, label: 'Matched Donors', value: '12', color: 'text-blue-500' },
    { icon: Heart, label: 'Fulfilled Requests', value: '28', color: 'text-green-500' },
    { icon: Clock, label: 'Avg Response Time', value: '2.5 hrs', color: 'text-purple-500' }
  ];

  const recentActivity = [
    {
      type: 'donation',
      message: 'Blood donation completed at City Hospital',
      time: '2 hours ago',
      icon: Heart,
      color: 'text-green-500'
    },
    {
      type: 'request',
      message: 'New blood request matched in your area',
      time: '1 day ago',
      icon: Activity,
      color: 'text-blue-500'
    },
    {
      type: 'alert',
      message: 'Urgent O- blood needed at Emergency Center',
      time: '2 days ago',
      icon: AlertCircle,
      color: 'text-red-500'
    }
  ];

  const stats = isDonor ? donorStats : hospitalStats;

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-800">
            Welcome back, {user?.name}! 👋
          </h1>
          <p className="text-gray-600 mt-2">
            {isDonor && "Thank you for being a life-saving donor."}
            {isHospital && "Manage your blood requests and find donors."}
            {isAdmin && "Monitor and manage the blood donation system."}
          </p>
        </div>

        {/* Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {stats.map((stat, index) => (
            <div key={index} className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">{stat.label}</p>
                  <p className="text-2xl font-bold text-gray-800 mt-1">{stat.value}</p>
                </div>
                <stat.icon className={`h-8 w-8 ${stat.color}`} />
              </div>
            </div>
          ))}
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Quick Actions */}
          <div className="lg:col-span-2">
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-6">Quick Actions</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {isDonor && (
                  <>
                    <button className="p-4 border-2 border-blue-200 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors text-left">
                      <Heart className="h-6 w-6 text-red-500 mb-2" />
                      <h3 className="font-semibold text-gray-800">Find Blood Requests</h3>
                      <p className="text-sm text-gray-600">Search for nearby blood requests</p>
                    </button>
                    <button className="p-4 border-2 border-green-200 rounded-lg hover:border-green-400 hover:bg-green-50 transition-colors text-left">
                      <Calendar className="h-6 w-6 text-green-500 mb-2" />
                      <h3 className="font-semibold text-gray-800">Schedule Donation</h3>
                      <p className="text-sm text-gray-600">Book your next donation</p>
                    </button>
                  </>
                )}
                {isHospital && (
                  <>
                    <button className="p-4 border-2 border-red-200 rounded-lg hover:border-red-400 hover:bg-red-50 transition-colors text-left">
                      <Activity className="h-6 w-6 text-red-500 mb-2" />
                      <h3 className="font-semibold text-gray-800">Create Blood Request</h3>
                      <p className="text-sm text-gray-600">Request blood for patients</p>
                    </button>
                    <button className="p-4 border-2 border-blue-200 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors text-left">
                      <Users className="h-6 w-6 text-blue-500 mb-2" />
                      <h3 className="font-semibold text-gray-800">View Donors</h3>
                      <p className="text-sm text-gray-600">Browse available donors</p>
                    </button>
                  </>
                )}
                <button className="p-4 border-2 border-purple-200 rounded-lg hover:border-purple-400 hover:bg-purple-50 transition-colors text-left">
                  <MapPin className="h-6 w-6 text-purple-500 mb-2" />
                  <h3 className="font-semibold text-gray-800">Update Location</h3>
                  <p className="text-sm text-gray-600">Keep your location current</p>
                </button>
                <button className="p-4 border-2 border-gray-200 rounded-lg hover:border-gray-400 hover:bg-gray-50 transition-colors text-left">
                  <Activity className="h-6 w-6 text-gray-500 mb-2" />
                  <h3 className="font-semibold text-gray-800">View Profile</h3>
                  <p className="text-sm text-gray-600">Manage your account</p>
                </button>
              </div>
            </div>
          </div>

          {/* Recent Activity */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-6">Recent Activity</h2>
              <div className="space-y-4">
                {recentActivity.map((activity, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50 transition-colors">
                    <activity.icon className={`h-5 w-5 ${activity.color} mt-0.5`} />
                    <div className="flex-1">
                      <p className="text-sm text-gray-800">{activity.message}</p>
                      <p className="text-xs text-gray-500 mt-1">{activity.time}</p>
                    </div>
                  </div>
                ))}
              </div>
              <button className="w-full mt-4 text-sm text-blue-600 hover:text-blue-800 font-medium">
                View All Activity
              </button>
            </div>
          </div>
        </div>

        {/* Emergency Alert */}
        <div className="mt-8 bg-red-50 border border-red-200 rounded-xl p-6">
          <div className="flex items-center space-x-3">
            <AlertCircle className="h-6 w-6 text-red-500" />
            <div>
              <h3 className="text-lg font-semibold text-red-800">Urgent Blood Needed</h3>
              <p className="text-red-700">
                Emergency blood shortage for O- type. 5 critical patients need immediate assistance.
              </p>
              <button className="mt-3 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors">
                Respond to Emergency
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default Dashboard;
