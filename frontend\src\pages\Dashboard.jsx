import { useAuth } from '../contexts/AuthContext';
import { AlertCircle } from 'lucide-react';

// Import role-specific dashboards
import DonorDashboard from '../components/DonorDashboard';
import HospitalDashboard from '../components/HospitalDashboard';
import AdminDashboard from './AdminDashboard';

const Dashboard = () => {
  const { user, isDonor, isHospital, isAdmin } = useAuth();

  // Route to appropriate dashboard based on user role
  if (isDonor) {
    return <DonorDashboard />;
  }

  if (isHospital) {
    return <HospitalDashboard />;
  }

  if (isAdmin) {
    return <AdminDashboard />;
  }

  // Fallback for unknown roles
  return (
    <div className="min-h-screen bg-gray-50 flex items-center justify-center">
      <div className="text-center">
        <AlertCircle className="h-16 w-16 text-red-500 mx-auto mb-4" />
        <h2 className="text-2xl font-bold text-gray-800 mb-4">Access Error</h2>
        <p className="text-gray-600">Unable to determine user role. Please contact support.</p>
      </div>
    </div>
  );
};

export default Dashboard;
