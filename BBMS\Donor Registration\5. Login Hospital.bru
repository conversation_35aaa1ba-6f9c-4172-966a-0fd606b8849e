meta {
  name: 5. <PERSON><PERSON> (Hospital)
  type: http
  seq: 5
}

post {
  url: http://localhost:3001/api/auth/login
  body: json
  auth: inherit
}

body:json {
  {
    "email": "<EMAIL>",
    "password": "dalmar123"
  }
}

tests {
  test("Should login hospital successfully", function() {
    expect(res.getStatus()).to.equal(200);
    expect(res.getBody().success).to.equal(true);
    expect(res.getBody().token).to.be.a('string');
  });
  
  test("Should save hospital token", function() {
    if (res.getBody().token) {
      bru.setVar("hospital_token", res.getBody().token);
    }
  });
}
