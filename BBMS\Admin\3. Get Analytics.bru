meta {
  name: 3. Get Analytics
  type: http
  seq: 3
}

get {
  url: http://localhost:3001/api/admin/analytics
  body: none
  auth: bearer
}

auth:bearer {
  token: {{admin_token}}
}

tests {
  test("Should get analytics successfully", function() {
    expect(res.getStatus()).to.equal(200);
    expect(res.getBody().success).to.equal(true);
    expect(res.getBody().bloodStats).to.be.an('array');
    expect(res.getBody().urgentRequests).to.be.a('number');
  });
  
  test("Should return blood statistics", function() {
    const bloodStats = res.getBody().bloodStats;
    if (bloodStats.length > 0) {
      expect(bloodStats[0]).to.have.property('_id');
      expect(bloodStats[0]).to.have.property('count');
    }
  });
}
