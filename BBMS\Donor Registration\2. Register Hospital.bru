meta {
  name: 2. Register Hospital
  type: http
  seq: 2
}

post {
  url: http://localhost:3001/api/auth/register-hospital
  body: json
  auth: inherit
}

body:json {
  {
    "name": "Medina Hospital",
    "email": "<EMAIL>",
    "password": "hospital123",
    "address": "Central Mogadishu",
    "phone": "+252 63 234 5678",
    "location": "Mogadishu, Somalia"
  }
}

tests {
  test("Should register hospital successfully", function() {
    expect(res.getStatus()).to.equal(201);
    expect(res.getBody().success).to.equal(true);
    expect(res.getBody().token).to.be.a('string');
  });
}
