meta {
  name: 2. Register Hospital
  type: http
  seq: 2
}

post {
  url: http://localhost:3001/api/auth/register-hospital
  body: json
  auth: inherit
}

body:json {
  {
    "name": "Shafi Specialist Hospital",
    "email": "<EMAIL>",
    "password": "shafi123",
    "address": "Banadir,Mogadishu",
    "phone": "555-0123",
    "location": "Somalia"
  }
}

tests {
  test("Should register hospital successfully", function() {
    expect(res.getStatus()).to.equal(201);
    expect(res.getBody().success).to.equal(true);
    expect(res.getBody().token).to.be.a('string');
  });
}
