meta {
  name: 2. Register Hospital
  type: http
  seq: 2
}

post {
  url: http://localhost:3001/api/auth/register-hospital
  body: json
  auth: inherit
}

body:json {
  {
  "name": "Dalmar Specialist Hospital",
    "email": "<EMAIL>",
    "password": "dalmar123",
    "address": "Banadir,Mogadishu",
    "phone": "6645",
    "location": "Xamar"
  }
}

tests {
  test("Should register hospital successfully", function() {
    expect(res.getStatus()).to.equal(201);
    expect(res.getBody().success).to.equal(true);
    expect(res.getBody().token).to.be.a('string');
  });
}
