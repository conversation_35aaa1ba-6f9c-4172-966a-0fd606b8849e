meta {
  name: 2. Register Hospital
  type: http
  seq: 2
}

post {
  url: http://localhost:5000/api/auth/register-hospital
  body: json
  auth: inherit
}

body:json {
  {
    "name": "City General Hospital",
    "email": "<EMAIL>",
    "password": "hospital123",
    "address": "123 Medical Center Dr, New York, NY",
    "phone": "555-0123",
    "location": "New York"
  }
}

tests {
  test("Should register hospital successfully", function() {
    expect(res.getStatus()).to.equal(201);
    expect(res.getBody().success).to.equal(true);
    expect(res.getBody().token).to.be.a('string');
  });
}
