import { Heart, Mail, Phone, MapPin } from 'lucide-react';

const Footer = () => {
  return (
    <footer className="bg-gray-800 text-white">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="col-span-1 md:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <Heart className="h-8 w-8 text-red-500 fill-current" />
              <span className="text-xl font-bold">
                🩸 <span style={{ color: '#4189DD' }}>SomDonate</span>
              </span>
            </div>
            <p className="text-gray-300 mb-4 max-w-md">
              Connecting blood donors with hospitals to save lives. Every donation counts, 
              and together we can make a difference in our community.
            </p>
            <div className="flex space-x-4">
              <div className="flex items-center space-x-2 text-sm text-gray-300">
                <Phone className="h-4 w-4" />
                <span>+****************</span>
              </div>
              <div className="flex items-center space-x-2 text-sm text-gray-300">
                <Mail className="h-4 w-4" />
                <span><EMAIL></span>
              </div>
            </div>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-lg font-semibold mb-4" style={{ color: '#4189DD' }}>
              Quick Links
            </h3>
            <ul className="space-y-2">
              <li>
                <a href="/" className="text-gray-300 hover:text-white transition-colors">
                  Home
                </a>
              </li>
              <li>
                <a href="/register" className="text-gray-300 hover:text-white transition-colors">
                  Become a Donor
                </a>
              </li>
              <li>
                <a href="/blood-requests" className="text-gray-300 hover:text-white transition-colors">
                  Find Blood Requests
                </a>
              </li>
              <li>
                <a href="/about" className="text-gray-300 hover:text-white transition-colors">
                  About Us
                </a>
              </li>
            </ul>
          </div>

          {/* Blood Types */}
          <div>
            <h3 className="text-lg font-semibold mb-4" style={{ color: '#4189DD' }}>
              Blood Types
            </h3>
            <div className="grid grid-cols-2 gap-2">
              {['A+', 'A-', 'B+', 'B-', 'AB+', 'AB-', 'O+', 'O-'].map((type) => (
                <div 
                  key={type}
                  className="bg-gray-700 text-center py-2 px-3 rounded text-sm font-medium"
                >
                  {type}
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-700 mt-8 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="text-gray-300 text-sm mb-4 md:mb-0">
              © 2024 SomDonate. All rights reserved. Saving lives, one donation at a time.
            </div>
            <div className="flex items-center space-x-2 text-sm text-gray-300">
              <MapPin className="h-4 w-4" />
              <span>Somalia, Mogadishu</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
