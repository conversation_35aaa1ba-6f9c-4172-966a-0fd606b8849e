# 🩸 BBMS API Testing Guide

## 📋 Prerequisites
1. **Server Running**: Make sure the backend server is running on port 5000
2. **Database Connected**: Ensure MongoDB connection is established
3. **Bruno Installed**: Have Bruno API client installed and ready

## 🚀 Testing Sequence

### Phase 1: Health Check
1. **0. Health Check** - Verify server is running

### Phase 2: User Registration & Authentication
Run these tests in order:

1. **1. Register Donor** - Creates a donor user
2. **2. Register Hospital** - Creates a hospital user  
3. **3. Register Admin** - Creates an admin user (initially as donor)
4. **4. <PERSON>gin (Donor)** - Login and save donor token
5. **5. <PERSON><PERSON> (Hospital)** - Login and save hospital token
6. **6. <PERSON><PERSON> (Admin)** - <PERSON>gin and save admin token

### Phase 3: Blood Request Management
1. **1. Create Blood Request** - Hospital creates a blood request
2. **2. Get Nearby Requests** - Donor searches for requests
3. **3. Fulfill Request** - Hospital fulfills the request

### Phase 4: Admin Operations
1. **1. Get All Users** - Admin views all users
2. **2. Verify Hospital** - Admin verifies hospital (need hospital_id)
3. **3. Get Analytics** - Admin views system analytics

### Phase 5: Error Testing
1. **1. Duplicate Email Registration** - Test duplicate email handling
2. **2. Invalid Login** - Test wrong credentials
3. **3. Unauthorized Access** - Test access without token

## 🔧 Important Notes

### Admin User Setup
After registering the admin user, you need to manually update their role in the database:
```javascript
// In MongoDB shell or Compass
db.users.updateOne(
  { email: "<EMAIL>" },
  { $set: { role: "admin" } }
)
```

### Hospital ID for Verification
To test hospital verification, you need the hospital's ObjectId:
```javascript
// Find hospital ID in MongoDB
db.hospitals.find({ email: "<EMAIL>" })
```
Then update the `hospital_id` variable in Bruno.

### Token Management
The tests automatically save tokens as variables:
- `donor_token` - For donor authentication
- `hospital_token` - For hospital authentication  
- `admin_token` - For admin authentication
- `request_id` - For request operations

## 📊 Expected Results

### Successful Registration Response:
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Successful Login Response:
```json
{
  "success": true,
  "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
}
```

### Blood Request Creation:
```json
{
  "success": true,
  "request": {
    "_id": "...",
    "hospitalId": "...",
    "bloodType": "O+",
    "units": 5,
    "urgency": "high",
    "status": "pending"
  }
}
```

### Analytics Response:
```json
{
  "success": true,
  "bloodStats": [
    { "_id": "O+", "count": 2 },
    { "_id": "A+", "count": 1 }
  ],
  "urgentRequests": 1
}
```

## 🐛 Troubleshooting

### Common Issues:
1. **Server not running**: Check if nodemon started successfully
2. **Database connection**: Verify MongoDB URI in .env
3. **Token expired**: Re-login to get fresh tokens
4. **Admin access denied**: Ensure user role is set to 'admin' in database
5. **Hospital verification fails**: Make sure you have the correct hospital_id

### Status Codes:
- `200` - Success
- `201` - Created successfully
- `400` - Bad request (duplicate email, validation error)
- `401` - Unauthorized (no token or invalid token)
- `403` - Forbidden (wrong role)
- `404` - Not found (user/resource doesn't exist)
- `500` - Server error

## 🎯 Testing Tips

1. **Run tests in sequence** - Some tests depend on previous ones
2. **Check response bodies** - Verify the data structure matches expectations
3. **Monitor server logs** - Watch the console for any errors
4. **Use variables** - Tokens and IDs are automatically saved for reuse
5. **Test error cases** - Don't forget to test the error scenarios

Happy Testing! 🚀
