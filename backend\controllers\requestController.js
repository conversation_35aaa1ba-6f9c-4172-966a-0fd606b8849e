const BloodRequest = require('../models/BloodRequest');
const User = require('../models/User');

exports.createRequest = async (req, res) => {
  try {
    const { bloodType, units, urgency } = req.body;

    const request = await BloodRequest.create({
      hospitalId: req.user.id,
      bloodType,
      units,
      urgency
    });

    res.status(201).json({ success: true, request });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};

exports.getNearbyRequests = async (req, res) => {
  try {
    const { bloodType, location } = req.query;

    const query = {
      bloodType,
      status: 'pending'
    };

    // Optional basic location filtering using string match
    if (location) {
      query["location"] = { $regex: new RegExp(location, "i") };
    }

    const requests = await BloodRequest.find(query).populate('hospitalId');

    res.json({ success: true, requests });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};

exports.fulfillRequest = async (req, res) => {
  try {
    const request = await BloodRequest.findByIdAndUpdate(
      req.params.id,
      { status: 'fulfilled' },
      { new: true }
    );

    if (!request) {
      return res.status(404).json({ success: false, message: 'Request not found' });
    }

    res.json({ success: true, request });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};
