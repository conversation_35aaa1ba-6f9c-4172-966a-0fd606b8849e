const BloodRequest = require('../models/BloodRequest');
const User = require('../models/User');

exports.createRequest = async (req, res) => {
  try {
    const { bloodType, units, urgency, description, deadline } = req.body;

    // Check if user is a hospital
    if (req.user.role !== 'hospital') {
      return res.status(403).json({
        success: false,
        message: 'Only hospitals can create blood requests'
      });
    }

    // Get hospital location from user profile
    const hospital = await User.findById(req.user.id);
    if (!hospital) {
      return res.status(404).json({
        success: false,
        message: 'Hospital not found'
      });
    }

    const request = await BloodRequest.create({
      hospital: req.user.id,  // Hospital ID from authenticated user
      bloodType,
      units,
      urgency: urgency || 'medium',
      description,
      location: hospital.location,  // Use hospital's location
      deadline: deadline ? new Date(deadline) : null
    });

    // Populate hospital details for response
    await request.populate('hospital', 'name location phone');

    res.status(201).json({
      success: true,
      message: 'Blood request created successfully',
      request
    });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};

exports.getNearbyRequests = async (req, res) => {
  try {
    const { bloodType, location } = req.query;

    const query = {
      bloodType,
      status: 'pending'
    };

    // Optional basic location filtering using string match
    if (location) {
      query["location"] = { $regex: new RegExp(location, "i") };
    }

    const requests = await BloodRequest.find(query).populate('hospital', 'name location phone');

    res.json({ success: true, requests });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};

exports.fulfillRequest = async (req, res) => {
  try {
    const request = await BloodRequest.findByIdAndUpdate(
      req.params.id,
      { status: 'fulfilled' },
      { new: true }
    );

    if (!request) {
      return res.status(404).json({ success: false, message: 'Request not found' });
    }

    res.json({ success: true, request });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};
