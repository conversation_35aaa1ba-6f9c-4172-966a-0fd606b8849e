const express = require('express');
const router = express.Router();
const adminController = require('../controllers/adminController');
const auth = require('../middleware/auth');

router.get('/users', auth(['admin']), adminController.getUsers);
router.patch('/verify-hospital/:id', auth(['admin']), adminController.verifyHospital);
router.get('/analytics', auth(['admin']), adminController.getAnalytics);

module.exports = router;
