const User = require('../models/User');
const Hospital = require('../models/Hospital');
const BloodRequest = require('../models/BloodRequest');

exports.getUsers = async (req, res) => {
  try {
    const users = await User.find();
    res.json({ success: true, users });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};

exports.verifyHospital = async (req, res) => {
  try {
    const hospital = await Hospital.findByIdAndUpdate(
      req.params.id,
      { verified: true },
      { new: true }
    );

    if (!hospital) {
      return res.status(404).json({ success: false, message: 'Hospital not found' });
    }

    res.json({ success: true, hospital });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};

exports.getAnalytics = async (req, res) => {
  try {
    const bloodStats = await BloodRequest.aggregate([
      { $group: { _id: '$bloodType', count: { $sum: 1 } } }
    ]);

    const urgentRequests = await BloodRequest.countDocuments({ urgency: 'high', status: 'pending' });

    res.json({
      success: true,
      bloodStats,
      urgentRequests
    });
  } catch (err) {
    res.status(500).json({ success: false, message: err.message });
  }
};
