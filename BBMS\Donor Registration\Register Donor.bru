meta {
  name: 1. Register Donor
  type: http
  seq: 1
}

post {
  url: http://localhost:5000/api/auth/register-donor
  body: json
  auth: inherit
}

body:json {
  {
    "name": "<PERSON>",
    "email": "<EMAIL>",
    "password": "password123",
    "bloodType": "O+",
    "location": "New York",
    "phone": "1234567890"
  }
}

tests {
  test("Should register donor successfully", function() {
    expect(res.getStatus()).to.equal(201);
    expect(res.getBody().success).to.equal(true);
    expect(res.getBody().token).to.be.a('string');
  });
}
