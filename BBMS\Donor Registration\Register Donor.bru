meta {
  name: 1. Register Donor
  type: http
  seq: 1
}

post {
  url: http://localhost:3001/api/auth/register-donor
  body: json
  auth: inherit
}

body:json {
  {
    "name": "<PERSON><PERSON><PERSON>",
    "email": "<EMAIL>",
    "password": "suldan123",
    "bloodType": "O+",
    "location": "<PERSON><PERSON><PERSON>",
    "phone": "12345678"
  }
}

tests {
  test("Should register donor successfully", function() {
    expect(res.getStatus()).to.equal(201);
    expect(res.getBody().success).to.equal(true);
    expect(res.getBody().token).to.be.a('string');
  });
}
