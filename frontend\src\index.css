@import "tailwindcss";

/* Custom CSS Variables for SomDonate */
:root {
  --primary-blue: #4189DD;
  --primary-white: #FFFFFF;
  --light-blue: #E3F2FD;
  --dark-blue: #2563EB;
  --success-green: #10B981;
  --warning-orange: #F59E0B;
  --error-red: #EF4444;
  --text-gray: #374151;
  --border-gray: #E5E7EB;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-blue);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--dark-blue);
}

/* Smooth transitions */
* {
  transition: all 0.2s ease-in-out;
}

/* Custom button animations */
.btn-primary {
  background: var(--primary-blue);
  color: var(--primary-white);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: var(--dark-blue);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(65, 137, 221, 0.3);
}

/* Blood drop animation */
@keyframes bloodDrop {
  0% { transform: translateY(-10px) scale(1); opacity: 0; }
  50% { transform: translateY(0) scale(1.1); opacity: 1; }
  100% { transform: translateY(10px) scale(1); opacity: 0; }
}

.blood-drop {
  animation: bloodDrop 2s infinite;
}

/* Admin Dashboard Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.8;
  }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0,0,0);
  }
  40%, 43% {
    transform: translate3d(0, -30px, 0);
  }
  70% {
    transform: translate3d(0, -15px, 0);
  }
  90% {
    transform: translate3d(0, -4px, 0);
  }
}

.animate-fadeIn {
  animation: fadeIn 0.5s ease-out;
}

.animate-slideIn {
  animation: slideIn 0.3s ease-out;
}

.animate-pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* Gradient backgrounds for cards */
.gradient-blue {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.gradient-red {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.gradient-green {
  background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.gradient-purple {
  background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
}

/* Hover effects for admin cards */
.admin-card {
  transition: all 0.3s ease;
}

.admin-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
}