@import "tailwindcss";

/* Custom CSS Variables for SomDonate */
:root {
  --primary-blue: #4189DD;
  --primary-white: #FFFFFF;
  --light-blue: #E3F2FD;
  --dark-blue: #2563EB;
  --success-green: #10B981;
  --warning-orange: #F59E0B;
  --error-red: #EF4444;
  --text-gray: #374151;
  --border-gray: #E5E7EB;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: var(--primary-blue);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--dark-blue);
}

/* Smooth transitions */
* {
  transition: all 0.2s ease-in-out;
}

/* Custom button animations */
.btn-primary {
  background: var(--primary-blue);
  color: var(--primary-white);
  border: none;
  padding: 0.75rem 1.5rem;
  border-radius: 0.5rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-primary:hover {
  background: var(--dark-blue);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(65, 137, 221, 0.3);
}

/* Blood drop animation */
@keyframes bloodDrop {
  0% { transform: translateY(-10px) scale(1); opacity: 0; }
  50% { transform: translateY(0) scale(1.1); opacity: 1; }
  100% { transform: translateY(10px) scale(1); opacity: 0; }
}

.blood-drop {
  animation: bloodDrop 2s infinite;
}