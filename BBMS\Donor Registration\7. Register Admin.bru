meta {
  name: 7. Register Admin
  type: http
  seq: 7
}

post {
  url: http://localhost:3001/api/auth/register-admin
  body: json
  auth: inherit
}

body:json {
  {
    "name": "New Admin User",
    "email": "<EMAIL>",
    "password": "admin123",
    "location": "Hargeisa, Somalia",
    "phone": "+252 63 555 5555"
  }
}

tests {
  test("Should register admin successfully", function() {
    expect(res.getStatus()).to.equal(201);
    expect(res.getBody().success).to.equal(true);
    expect(res.getBody().token).to.be.a('string');
    expect(res.getBody().user.role).to.equal('admin');
    expect(res.getBody().message).to.include('Admin user created successfully');
  });
  
  test("Should return admin user details", function() {
    expect(res.getBody().user.name).to.equal('New Admin User');
    expect(res.getBody().user.email).to.equal('<EMAIL>');
    expect(res.getBody().user.role).to.equal('admin');
  });
}
