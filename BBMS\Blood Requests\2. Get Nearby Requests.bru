meta {
  name: 2. Get Nearby Requests
  type: http
  seq: 2
}

get {
  url: http://localhost:3001/api/requests/nearby?bloodType=O+&location=New York
  body: none
  auth: bearer
}

params:query {
  bloodType: O+
  location: New York
}

auth:bearer {
  token: {{donor_token}}
}

tests {
  test("Should get nearby requests successfully", function() {
    expect(res.getStatus()).to.equal(200);
    expect(res.getBody().success).to.equal(true);
    expect(res.getBody().requests).to.be.an('array');
  });
  
  test("Should return requests with correct blood type", function() {
    const requests = res.getBody().requests;
    if (requests.length > 0) {
      expect(requests[0].bloodType).to.equal("O+");
      expect(requests[0].status).to.equal("pending");
    }
  });
}
