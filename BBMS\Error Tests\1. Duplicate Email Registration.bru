meta {
  name: 1. Duplicate Email Registration
  type: http
  seq: 1
}

post {
  url: http://localhost:3001/api/auth/register-donor
  body: json
  auth: inherit
}

body:json {
  {
    "name": "<PERSON> Duplicate",
    "email": "<EMAIL>",
    "password": "password123",
    "bloodType": "A+",
    "location": "Boston",
    "phone": "9876543210"
  }
}

tests {
  test("Should reject duplicate email", function() {
    expect(res.getStatus()).to.equal(400);
    expect(res.getBody().success).to.equal(false);
    expect(res.getBody().message).to.include("Email already in use");
  });
}
