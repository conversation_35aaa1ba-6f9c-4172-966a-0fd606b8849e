import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';
import {
  Users,
  Activity,
  Heart,
  Building,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  BarChart3,
  Shield,
  Search,
  Filter,
  Ban,
  Trash2,
  Eye,
  Send,
  Bell,
  MapPin,
  Calendar,
  Target,
  Flag,
  Settings,
  Database,
  Globe,
  Zap,
  Menu,
  X,
  LogOut,
  User,
  FileText,
  Home,
  ChevronRight,
  PieChart,
  BarChart,
  LineChart,
  DollarSign,
  UserPlus,
  Mail,
  Phone
} from 'lucide-react';

const AdminDashboard = () => {
  const { user, logout } = useAuth();
  const [activeTab, setActiveTab] = useState('dashboard');
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState('all');
  const [sidebarOpen, setSidebarOpen] = useState(true);

  // State for all admin data
  const [systemStats, setSystemStats] = useState({
    totalUsers: 0,
    totalDonors: 0,
    totalHospitals: 0,
    activeRequests: 0,
    totalDonations: 0,
    pendingVerifications: 0
  });

  const [allUsers, setAllUsers] = useState([]);
  const [allHospitals, setAllHospitals] = useState([]);
  const [allRequests, setAllRequests] = useState([]);
  const [pendingHospitals, setPendingHospitals] = useState([]);
  const [analytics, setAnalytics] = useState({
    bloodTypeStats: [],
    monthlyDonations: [],
    regionalData: [],
    urgentRequests: 0
  });

  // Fetch all admin data
  useEffect(() => {
    const fetchAdminData = async () => {
      try {
        // TODO: Replace with real API calls
        // const [usersRes, hospitalsRes, requestsRes, analyticsRes] = await Promise.all([
        //   axios.get('/api/admin/users'),
        //   axios.get('/api/admin/hospitals'),
        //   axios.get('/api/admin/requests'),
        //   axios.get('/api/admin/analytics')
        // ]);

        // Mock data for comprehensive admin dashboard
        setSystemStats({
          totalUsers: 2847,
          totalDonors: 2802,
          totalHospitals: 45,
          activeRequests: 23,
          totalDonations: 1234,
          pendingVerifications: 3
        });

        setAllUsers([
          { id: 1, name: 'John Doe', email: '<EMAIL>', type: 'Donor', bloodType: 'O+', status: 'Active', joinDate: '2024-01-15', location: 'New York', lastActive: '2024-01-20' },
          { id: 2, name: 'Ahmed Hassan', email: '<EMAIL>', type: 'Donor', bloodType: 'A-', status: 'Active', joinDate: '2024-01-14', location: 'Mogadishu', lastActive: '2024-01-19' },
          { id: 3, name: 'Fatima Ali', email: '<EMAIL>', type: 'Donor', bloodType: 'B+', status: 'Inactive', joinDate: '2024-01-13', location: 'Hargeisa', lastActive: '2024-01-15' },
          { id: 4, name: 'Mohamed Omar', email: '<EMAIL>', type: 'Donor', bloodType: 'AB-', status: 'Banned', joinDate: '2024-01-12', location: 'Bosaso', lastActive: '2024-01-14' },
          { id: 5, name: 'City General Hospital', email: '<EMAIL>', type: 'Hospital', location: 'New York', status: 'Verified', joinDate: '2024-01-10', lastActive: '2024-01-20' }
        ]);

        setPendingHospitals([
          { id: 1, name: 'Kalkaal Hospital', email: '<EMAIL>', location: 'Mogadishu, Somalia', requestDate: '2024-01-18', documents: 'Complete', phone: '+252 61 123 4567' },
          { id: 2, name: 'Medina Hospital', email: '<EMAIL>', location: 'Hargeisa, Somalia', requestDate: '2024-01-17', documents: 'Pending', phone: '+252 63 234 5678' },
          { id: 3, name: 'Bosaso Medical Center', email: '<EMAIL>', location: 'Bosaso, Somalia', requestDate: '2024-01-16', documents: 'Complete', phone: '+252 90 345 6789' }
        ]);

        setAllRequests([
          { id: 1, hospitalName: 'City General Hospital', bloodType: 'O+', units: 3, urgency: 'high', status: 'pending', datePosted: '2024-01-20', location: 'New York' },
          { id: 2, hospitalName: 'Kalkaal Hospital', bloodType: 'A-', units: 2, urgency: 'medium', status: 'pending', datePosted: '2024-01-19', location: 'Mogadishu' },
          { id: 3, hospitalName: 'Medina Hospital', bloodType: 'B+', units: 1, urgency: 'low', status: 'fulfilled', datePosted: '2024-01-18', location: 'Hargeisa' },
          { id: 4, hospitalName: 'Metro Medical', bloodType: 'AB-', units: 4, urgency: 'high', status: 'pending', datePosted: '2024-01-17', location: 'Brooklyn' }
        ]);

        setAnalytics({
          bloodTypeStats: [
            { bloodType: 'O+', requests: 45, donors: 890, shortage: false },
            { bloodType: 'O-', requests: 23, donors: 156, shortage: true },
            { bloodType: 'A+', requests: 34, donors: 567, shortage: false },
            { bloodType: 'A-', requests: 18, donors: 234, shortage: true },
            { bloodType: 'B+', requests: 28, donors: 445, shortage: false },
            { bloodType: 'B-', requests: 12, donors: 123, shortage: true },
            { bloodType: 'AB+', requests: 15, donors: 234, shortage: false },
            { bloodType: 'AB-', requests: 8, donors: 89, shortage: true }
          ],
          monthlyDonations: [
            { month: 'Jan', donations: 234 },
            { month: 'Feb', donations: 267 },
            { month: 'Mar', donations: 298 },
            { month: 'Apr', donations: 245 },
            { month: 'May', donations: 312 },
            { month: 'Jun', donations: 289 }
          ],
          regionalData: [
            { region: 'Mogadishu', donors: 1245, requests: 67, shortage: 'O-' },
            { region: 'Hargeisa', donors: 567, requests: 34, shortage: 'A-' },
            { region: 'Bosaso', donors: 234, requests: 12, shortage: 'B-' },
            { region: 'Kismayo', donors: 189, requests: 8, shortage: 'AB-' }
          ],
          urgentRequests: 5
        });

      } catch (error) {
        console.error('Error fetching admin data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAdminData();
  }, []);

  // Filter users based on search and role
  const filteredUsers = allUsers.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = filterRole === 'all' || user.type.toLowerCase() === filterRole.toLowerCase();
    return matchesSearch && matchesRole;
  });

  // Admin action handlers
  const handleVerifyHospital = async (hospitalId) => {
    try {
      // TODO: API call to verify hospital
      // await axios.patch(`/api/admin/verify-hospital/${hospitalId}`);

      setPendingHospitals(prev => prev.filter(h => h.id !== hospitalId));
      alert('Hospital verified successfully!');
    } catch (error) {
      console.error('Error verifying hospital:', error);
    }
  };

  const handleRejectHospital = async (hospitalId) => {
    try {
      // TODO: API call to reject hospital
      setPendingHospitals(prev => prev.filter(h => h.id !== hospitalId));
      alert('Hospital application rejected.');
    } catch (error) {
      console.error('Error rejecting hospital:', error);
    }
  };

  const handleBanUser = async (userId) => {
    try {
      // TODO: API call to ban user
      setAllUsers(prev => prev.map(user =>
        user.id === userId ? { ...user, status: 'Banned' } : user
      ));
      alert('User banned successfully!');
    } catch (error) {
      console.error('Error banning user:', error);
    }
  };

  const handleDeleteUser = async (userId) => {
    if (window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      try {
        // TODO: API call to delete user
        setAllUsers(prev => prev.filter(user => user.id !== userId));
        alert('User deleted successfully!');
      } catch (error) {
        console.error('Error deleting user:', error);
      }
    }
  };

  const handleSendGlobalAlert = () => {
    // TODO: Implement global alert functionality
    alert('Global alert feature coming soon!');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  // Sidebar menu items
  const sidebarItems = [
    { id: 'dashboard', label: 'Dashboard', icon: Home, active: true },
    { id: 'users', label: 'All Users (Donors)', icon: Users },
    { id: 'hospitals', label: 'All Hospitals', icon: Building },
    { id: 'reports', label: 'Reports', icon: FileText },
    { id: 'settings', label: 'Settings', icon: Settings },
    { id: 'profile', label: 'Profile', icon: User }
  ];

  return (
    <div className="min-h-screen bg-gray-100 flex">
      {/* Sidebar */}
      <div className={`${sidebarOpen ? 'w-64' : 'w-20'} bg-white shadow-xl transition-all duration-300 ease-in-out flex flex-col`}>
        {/* Sidebar Header */}
        <div className="p-6 border-b border-gray-200">
          <div className="flex items-center space-x-3">
            <div className="bg-blue-600 p-2 rounded-lg">
              <Shield className="h-6 w-6 text-white" />
            </div>
            {sidebarOpen && (
              <div>
                <h2 className="text-lg font-bold text-gray-800">🩸 SomDonate</h2>
                <p className="text-sm text-gray-600">Admin Panel</p>
              </div>
            )}
          </div>
        </div>

        {/* Sidebar Menu */}
        <nav className="flex-1 p-4">
          <ul className="space-y-2">
            {sidebarItems.map((item) => (
              <li key={item.id}>
                <button
                  onClick={() => setActiveTab(item.id)}
                  className={`w-full flex items-center space-x-3 px-4 py-3 rounded-lg transition-all duration-200 ${
                    activeTab === item.id
                      ? 'bg-blue-50 text-blue-600 border-r-4 border-blue-600'
                      : 'text-gray-700 hover:bg-gray-50 hover:text-blue-600'
                  }`}
                >
                  <item.icon className="h-5 w-5" />
                  {sidebarOpen && <span className="font-medium">{item.label}</span>}
                  {sidebarOpen && activeTab === item.id && (
                    <ChevronRight className="h-4 w-4 ml-auto" />
                  )}
                </button>
              </li>
            ))}
          </ul>
        </nav>

        {/* Sidebar Footer */}
        <div className="p-4 border-t border-gray-200">
          <button
            onClick={logout}
            className="w-full flex items-center space-x-3 px-4 py-3 text-red-600 hover:bg-red-50 rounded-lg transition-colors"
          >
            <LogOut className="h-5 w-5" />
            {sidebarOpen && <span className="font-medium">Logout</span>}
          </button>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 flex flex-col">
        {/* Top Bar */}
        <header className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <button
                onClick={() => setSidebarOpen(!sidebarOpen)}
                className="p-2 rounded-lg hover:bg-gray-100 transition-colors"
              >
                <Menu className="h-5 w-5 text-gray-600" />
              </button>
              <div>
                <h1 className="text-2xl font-bold text-gray-800">
                  {sidebarItems.find(item => item.id === activeTab)?.label || 'Dashboard'}
                </h1>
                <p className="text-sm text-gray-600">Welcome back, {user?.name}</p>
              </div>
            </div>

            <div className="flex items-center space-x-4">
              {/* Search Bar */}
              <div className="relative">
                <Search className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                <input
                  type="text"
                  placeholder="Search..."
                  className="pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              </div>

              {/* Notifications */}
              <button className="relative p-2 rounded-lg hover:bg-gray-100 transition-colors">
                <Bell className="h-5 w-5 text-gray-600" />
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
                  3
                </span>
              </button>

              {/* Profile */}
              <div className="flex items-center space-x-3">
                <div className="bg-blue-600 p-2 rounded-full">
                  <User className="h-5 w-5 text-white" />
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-800">{user?.name}</p>
                  <p className="text-xs text-gray-600">Administrator</p>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* Main Content Area */}
        <main className="flex-1 p-6 bg-gray-50 overflow-auto">
          {/* Dashboard Content */}
          {activeTab === 'dashboard' && (
            <div className="space-y-6">
              {/* Stats Cards with Animation */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
                <div className="bg-gradient-to-r from-blue-500 to-blue-600 p-6 rounded-xl shadow-lg text-white transform hover:scale-105 transition-all duration-300 animate-pulse">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-blue-100 text-sm">Total Users</p>
                      <p className="text-3xl font-bold">{systemStats.totalUsers.toLocaleString()}</p>
                      <p className="text-blue-100 text-sm">+12% this month</p>
                    </div>
                    <div className="bg-white bg-opacity-20 p-3 rounded-full">
                      <Users className="h-8 w-8" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-red-500 to-red-600 p-6 rounded-xl shadow-lg text-white transform hover:scale-105 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-red-100 text-sm">Blood Donors</p>
                      <p className="text-3xl font-bold">{systemStats.totalDonors.toLocaleString()}</p>
                      <p className="text-red-100 text-sm">+8% this month</p>
                    </div>
                    <div className="bg-white bg-opacity-20 p-3 rounded-full">
                      <Heart className="h-8 w-8" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-green-500 to-green-600 p-6 rounded-xl shadow-lg text-white transform hover:scale-105 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-green-100 text-sm">Hospitals</p>
                      <p className="text-3xl font-bold">{systemStats.totalHospitals}</p>
                      <p className="text-green-100 text-sm">+3 verified</p>
                    </div>
                    <div className="bg-white bg-opacity-20 p-3 rounded-full">
                      <Building className="h-8 w-8" />
                    </div>
                  </div>
                </div>

                <div className="bg-gradient-to-r from-purple-500 to-purple-600 p-6 rounded-xl shadow-lg text-white transform hover:scale-105 transition-all duration-300">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-purple-100 text-sm">Active Requests</p>
                      <p className="text-3xl font-bold">{systemStats.activeRequests}</p>
                      <p className="text-purple-100 text-sm">{analytics.urgentRequests} urgent</p>
                    </div>
                    <div className="bg-white bg-opacity-20 p-3 rounded-full">
                      <Activity className="h-8 w-8" />
                    </div>
                  </div>
                </div>
              </div>

              {/* Charts and Analytics */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Blood Type Distribution Chart */}
                <div className="bg-white p-6 rounded-xl shadow-lg">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-bold text-gray-800">Blood Type Distribution</h3>
                    <PieChart className="h-5 w-5 text-blue-600" />
                  </div>
                  <div className="space-y-4">
                    {analytics.bloodTypeStats.slice(0, 4).map((stat, index) => (
                      <div key={index} className="flex items-center justify-between">
                        <div className="flex items-center space-x-3">
                          <div className={`w-4 h-4 rounded-full ${
                            index === 0 ? 'bg-red-500' :
                            index === 1 ? 'bg-blue-500' :
                            index === 2 ? 'bg-green-500' : 'bg-yellow-500'
                          }`}></div>
                          <span className="font-semibold text-gray-800">{stat.bloodType}</span>
                        </div>
                        <div className="text-right">
                          <p className="font-bold text-gray-800">{stat.donors}</p>
                          <p className="text-sm text-gray-600">donors</p>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Monthly Donations Chart */}
                <div className="bg-white p-6 rounded-xl shadow-lg">
                  <div className="flex items-center justify-between mb-6">
                    <h3 className="text-lg font-bold text-gray-800">Monthly Donations</h3>
                    <BarChart className="h-5 w-5 text-green-600" />
                  </div>
                  <div className="space-y-3">
                    {analytics.monthlyDonations.map((month, index) => (
                      <div key={index} className="flex items-center space-x-3">
                        <span className="w-12 text-sm text-gray-600">{month.month}</span>
                        <div className="flex-1 bg-gray-200 rounded-full h-3">
                          <div
                            className="bg-gradient-to-r from-green-400 to-green-600 h-3 rounded-full transition-all duration-1000 ease-out"
                            style={{ width: `${(month.donations / 350) * 100}%` }}
                          ></div>
                        </div>
                        <span className="text-sm font-semibold text-gray-800">{month.donations}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </div>

              {/* Recent Activity and Quick Actions */}
              <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
                {/* Recent Activity */}
                <div className="lg:col-span-2 bg-white p-6 rounded-xl shadow-lg">
                  <h3 className="text-lg font-bold text-gray-800 mb-6 flex items-center space-x-2">
                    <Activity className="h-5 w-5" />
                    <span>Recent Activity</span>
                  </h3>
                  <div className="space-y-4">
                    <div className="flex items-start space-x-3 p-3 border-l-4 border-green-400 bg-green-50 rounded animate-fadeIn">
                      <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-800">Hospital Verified</p>
                        <p className="text-xs text-gray-600">Kalkaal Hospital approved - 2 hours ago</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3 p-3 border-l-4 border-blue-400 bg-blue-50 rounded animate-fadeIn">
                      <UserPlus className="h-5 w-5 text-blue-500 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-800">New Donor Registration</p>
                        <p className="text-xs text-gray-600">Ahmed Hassan (O+) joined - 4 hours ago</p>
                      </div>
                    </div>
                    <div className="flex items-start space-x-3 p-3 border-l-4 border-red-400 bg-red-50 rounded animate-fadeIn">
                      <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
                      <div>
                        <p className="text-sm font-medium text-gray-800">Urgent Blood Request</p>
                        <p className="text-xs text-gray-600">O- needed at Metro Medical - 6 hours ago</p>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Quick Actions */}
                <div className="bg-white p-6 rounded-xl shadow-lg">
                  <h3 className="text-lg font-bold text-gray-800 mb-6">Quick Actions</h3>
                  <div className="space-y-3">
                    <button className="w-full p-4 bg-gradient-to-r from-red-500 to-red-600 text-white rounded-lg hover:from-red-600 hover:to-red-700 transition-all duration-300 transform hover:scale-105">
                      <Bell className="h-5 w-5 mx-auto mb-2" />
                      <span className="text-sm font-medium">Send Alert</span>
                    </button>
                    <button
                      onClick={() => setActiveTab('hospitals')}
                      className="w-full p-4 bg-gradient-to-r from-blue-500 to-blue-600 text-white rounded-lg hover:from-blue-600 hover:to-blue-700 transition-all duration-300 transform hover:scale-105"
                    >
                      <Building className="h-5 w-5 mx-auto mb-2" />
                      <span className="text-sm font-medium">Verify Hospitals</span>
                    </button>
                    <button
                      onClick={() => setActiveTab('reports')}
                      className="w-full p-4 bg-gradient-to-r from-green-500 to-green-600 text-white rounded-lg hover:from-green-600 hover:to-green-700 transition-all duration-300 transform hover:scale-105"
                    >
                      <FileText className="h-5 w-5 mx-auto mb-2" />
                      <span className="text-sm font-medium">View Reports</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* All Users (Donors) Tab */}
          {activeTab === 'users' && (
            <div className="bg-white rounded-xl shadow-lg p-6">
              <div className="flex justify-between items-center mb-6">
                <h2 className="text-xl font-bold text-gray-800 flex items-center space-x-2">
                  <Users className="h-5 w-5" />
                  <span>All Users (Donors)</span>
                </h2>
                <div className="flex space-x-3">
                  <select
                    value={filterRole}
                    onChange={(e) => setFilterRole(e.target.value)}
                    className="px-3 py-2 border border-gray-300 rounded-lg"
                  >
                    <option value="all">All Users</option>
                    <option value="donor">Donors</option>
                    <option value="hospital">Hospitals</option>
                  </select>
                  <div className="relative">
                    <Search className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                    <input
                      type="text"
                      placeholder="Search users..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 pr-3 py-2 border border-gray-300 rounded-lg"
                    />
                  </div>
                </div>
              </div>

              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b border-gray-200">
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">User</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Type</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Blood Type</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Location</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Status</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Last Active</th>
                      <th className="text-left py-3 px-4 font-semibold text-gray-700">Actions</th>
                    </tr>
                  </thead>
                  <tbody>
                    {filteredUsers.map((user) => (
                      <tr key={user.id} className="border-b border-gray-100 hover:bg-gray-50 transition-colors">
                        <td className="py-3 px-4">
                          <div>
                            <p className="font-semibold text-gray-800">{user.name}</p>
                            <p className="text-sm text-gray-600">{user.email}</p>
                          </div>
                        </td>
                        <td className="py-3 px-4">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            user.type === 'Donor' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'
                          }`}>
                            {user.type}
                          </span>
                        </td>
                        <td className="py-3 px-4">
                          {user.bloodType ? (
                            <span className="font-semibold text-red-600">{user.bloodType}</span>
                          ) : (
                            <span className="text-gray-400">N/A</span>
                          )}
                        </td>
                        <td className="py-3 px-4 text-sm text-gray-600">{user.location}</td>
                        <td className="py-3 px-4">
                          <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                            user.status === 'Active' ? 'bg-green-100 text-green-800' :
                            user.status === 'Verified' ? 'bg-blue-100 text-blue-800' :
                            user.status === 'Banned' ? 'bg-red-100 text-red-800' :
                            'bg-gray-100 text-gray-800'
                          }`}>
                            {user.status}
                          </span>
                        </td>
                        <td className="py-3 px-4 text-sm text-gray-600">
                          {new Date(user.lastActive).toLocaleDateString()}
                        </td>
                        <td className="py-3 px-4">
                          <div className="flex space-x-2">
                            <button className="text-blue-600 hover:text-blue-800 text-sm transition-colors">
                              <Eye className="h-4 w-4" />
                            </button>
                            {user.status !== 'Banned' && (
                              <button
                                onClick={() => handleBanUser(user.id)}
                                className="text-yellow-600 hover:text-yellow-800 text-sm transition-colors"
                              >
                                <Ban className="h-4 w-4" />
                              </button>
                            )}
                            <button
                              onClick={() => handleDeleteUser(user.id)}
                              className="text-red-600 hover:text-red-800 text-sm transition-colors"
                            >
                              <Trash2 className="h-4 w-4" />
                            </button>
                          </div>
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {filteredUsers.length === 0 && (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-600">No users found matching your criteria.</p>
                </div>
              )}
            </div>
          )}

          {/* All Hospitals Tab */}
          {activeTab === 'hospitals' && (
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2">
                <Building className="h-5 w-5" />
                <span>Hospital Verification Center</span>
              </h2>

              {pendingHospitals.length > 0 ? (
                <div className="space-y-6">
                  <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                    <div className="flex items-center space-x-2">
                      <AlertCircle className="h-5 w-5 text-yellow-600" />
                      <p className="text-yellow-800 font-medium">
                        {pendingHospitals.length} hospitals awaiting verification
                      </p>
                    </div>
                  </div>

                  {pendingHospitals.map((hospital) => (
                    <div key={hospital.id} className="border border-gray-200 rounded-lg p-6 hover:shadow-md transition-shadow">
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                        <div>
                          <h3 className="text-lg font-bold text-gray-800 mb-3">{hospital.name}</h3>
                          <div className="space-y-2">
                            <div className="flex items-center space-x-2">
                              <Mail className="h-4 w-4 text-gray-500" />
                              <span className="text-gray-700">{hospital.email}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <MapPin className="h-4 w-4 text-gray-500" />
                              <span className="text-gray-700">{hospital.location}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Phone className="h-4 w-4 text-gray-500" />
                              <span className="text-gray-700">{hospital.phone}</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Calendar className="h-4 w-4 text-gray-500" />
                              <span className="text-gray-700">Applied: {new Date(hospital.requestDate).toLocaleDateString()}</span>
                            </div>
                          </div>
                        </div>

                        <div>
                          <h4 className="font-semibold text-gray-800 mb-3">Verification Status</h4>
                          <div className="space-y-2">
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Documents</span>
                              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                                hospital.documents === 'Complete'
                                  ? 'bg-green-100 text-green-800'
                                  : 'bg-yellow-100 text-yellow-800'
                              }`}>
                                {hospital.documents}
                              </span>
                            </div>
                            <div className="flex justify-between items-center">
                              <span className="text-gray-600">Background Check</span>
                              <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                In Progress
                              </span>
                            </div>
                          </div>

                          <div className="flex space-x-3 mt-6">
                            <button
                              onClick={() => handleVerifyHospital(hospital.id)}
                              className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium flex items-center justify-center space-x-2"
                            >
                              <CheckCircle className="h-4 w-4" />
                              <span>Approve</span>
                            </button>
                            <button
                              onClick={() => handleRejectHospital(hospital.id)}
                              className="flex-1 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors font-medium flex items-center justify-center space-x-2"
                            >
                              <AlertCircle className="h-4 w-4" />
                              <span>Reject</span>
                            </button>
                          </div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12">
                  <Building className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-800 mb-2">All Caught Up!</h3>
                  <p className="text-gray-600">No hospitals pending verification at the moment.</p>
                </div>
              )}
            </div>
          )}

          {/* Reports Tab */}
          {activeTab === 'reports' && (
            <div className="space-y-6">
              {/* Blood Type Analytics */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2">
                  <BarChart3 className="h-5 w-5" />
                  <span>Blood Type Analytics</span>
                </h2>

                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                  {analytics.bloodTypeStats.map((stat, index) => (
                    <div key={index} className={`p-4 rounded-lg border-2 transform hover:scale-105 transition-all duration-300 ${
                      stat.shortage ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'
                    }`}>
                      <div className="flex justify-between items-center mb-2">
                        <span className="text-2xl font-bold text-gray-800">{stat.bloodType}</span>
                        {stat.shortage && <AlertCircle className="h-5 w-5 text-red-500" />}
                      </div>
                      <div className="space-y-1">
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Requests:</span>
                          <span className="font-semibold">{stat.requests}</span>
                        </div>
                        <div className="flex justify-between text-sm">
                          <span className="text-gray-600">Donors:</span>
                          <span className="font-semibold">{stat.donors}</span>
                        </div>
                        <div className="text-xs mt-2">
                          {stat.shortage ? (
                            <span className="text-red-600 font-medium">⚠️ Critical Shortage</span>
                          ) : (
                            <span className="text-green-600 font-medium">✅ Adequate Supply</span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              {/* Monthly Trends */}
              <div className="bg-white rounded-xl shadow-lg p-6">
                <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2">
                  <TrendingUp className="h-5 w-5" />
                  <span>Monthly Donation Trends</span>
                </h2>

                <div className="grid grid-cols-6 gap-4">
                  {analytics.monthlyDonations.map((month, index) => (
                    <div key={index} className="text-center">
                      <div className="bg-gradient-to-t from-blue-400 to-blue-600 rounded-lg p-4 mb-2 text-white transform hover:scale-110 transition-all duration-300">
                        <div className="text-2xl font-bold">{month.donations}</div>
                      </div>
                      <div className="text-sm text-gray-600">{month.month}</div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          )}

          {/* Settings Tab */}
          {activeTab === 'settings' && (
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>System Settings</span>
              </h2>

              <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">General Settings</h3>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">System Name</label>
                      <input
                        type="text"
                        defaultValue="🩸 SomDonate"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Admin Email</label>
                      <input
                        type="email"
                        defaultValue="<EMAIL>"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Emergency Hotline</label>
                      <input
                        type="tel"
                        defaultValue="+252 61 911 0000"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-semibold text-gray-800 mb-4">Notification Settings</h3>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <span className="text-gray-700">Email Notifications</span>
                      <input type="checkbox" defaultChecked className="rounded" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-700">SMS Alerts</span>
                      <input type="checkbox" defaultChecked className="rounded" />
                    </div>
                    <div className="flex items-center justify-between">
                      <span className="text-gray-700">Emergency Broadcasts</span>
                      <input type="checkbox" defaultChecked className="rounded" />
                    </div>
                  </div>
                </div>
              </div>

              <div className="mt-8 pt-6 border-t border-gray-200">
                <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                  Save Settings
                </button>
              </div>
            </div>
          )}

          {/* Profile Tab */}
          {activeTab === 'profile' && (
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2">
                <User className="h-5 w-5" />
                <span>Admin Profile</span>
              </h2>

              <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
                <div className="text-center">
                  <div className="bg-blue-600 p-8 rounded-full w-32 h-32 mx-auto mb-4 flex items-center justify-center">
                    <User className="h-16 w-16 text-white" />
                  </div>
                  <h3 className="text-lg font-bold text-gray-800">{user?.name}</h3>
                  <p className="text-gray-600">System Administrator</p>
                  <button className="mt-4 bg-blue-600 text-white px-4 py-2 rounded-lg hover:bg-blue-700 transition-colors">
                    Change Photo
                  </button>
                </div>

                <div className="lg:col-span-2">
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Full Name</label>
                      <input
                        type="text"
                        defaultValue={user?.name}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Email Address</label>
                      <input
                        type="email"
                        defaultValue={user?.email}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Phone Number</label>
                      <input
                        type="tel"
                        defaultValue="+252 61 123 4567"
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Role</label>
                      <input
                        type="text"
                        value="System Administrator"
                        disabled
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg bg-gray-100"
                      />
                    </div>
                  </div>

                  <div className="mt-8 pt-6 border-t border-gray-200">
                    <div className="flex space-x-4">
                      <button className="bg-blue-600 text-white px-6 py-2 rounded-lg hover:bg-blue-700 transition-colors font-medium">
                        Update Profile
                      </button>
                      <button className="bg-gray-600 text-white px-6 py-2 rounded-lg hover:bg-gray-700 transition-colors font-medium">
                        Change Password
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
        </main>
      </div>
    </div>
  );
};

export default AdminDashboard;
