import { useState, useEffect } from 'react';
import { useAuth } from '../contexts/AuthContext';
import axios from 'axios';
import {
  Users,
  Activity,
  Heart,
  Building,
  TrendingUp,
  AlertCircle,
  CheckCircle,
  Clock,
  BarChart3,
  Shield,
  Search,
  Filter,
  Ban,
  Trash2,
  Eye,
  Send,
  Bell,
  MapPin,
  Calendar,
  Target,
  Flag,
  Settings,
  Database,
  Globe,
  Zap
} from 'lucide-react';

const AdminDashboard = () => {
  const { user } = useAuth();
  const [activeTab, setActiveTab] = useState('overview');
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [filterRole, setFilterRole] = useState('all');

  // State for all admin data
  const [systemStats, setSystemStats] = useState({
    totalUsers: 0,
    totalDonors: 0,
    totalHospitals: 0,
    activeRequests: 0,
    totalDonations: 0,
    pendingVerifications: 0
  });

  const [allUsers, setAllUsers] = useState([]);
  const [allHospitals, setAllHospitals] = useState([]);
  const [allRequests, setAllRequests] = useState([]);
  const [pendingHospitals, setPendingHospitals] = useState([]);
  const [analytics, setAnalytics] = useState({
    bloodTypeStats: [],
    monthlyDonations: [],
    regionalData: [],
    urgentRequests: 0
  });

  // Fetch all admin data
  useEffect(() => {
    const fetchAdminData = async () => {
      try {
        // TODO: Replace with real API calls
        // const [usersRes, hospitalsRes, requestsRes, analyticsRes] = await Promise.all([
        //   axios.get('/api/admin/users'),
        //   axios.get('/api/admin/hospitals'),
        //   axios.get('/api/admin/requests'),
        //   axios.get('/api/admin/analytics')
        // ]);

        // Mock data for comprehensive admin dashboard
        setSystemStats({
          totalUsers: 2847,
          totalDonors: 2802,
          totalHospitals: 45,
          activeRequests: 23,
          totalDonations: 1234,
          pendingVerifications: 3
        });

        setAllUsers([
          { id: 1, name: 'John Doe', email: '<EMAIL>', type: 'Donor', bloodType: 'O+', status: 'Active', joinDate: '2024-01-15', location: 'New York', lastActive: '2024-01-20' },
          { id: 2, name: 'Ahmed Hassan', email: '<EMAIL>', type: 'Donor', bloodType: 'A-', status: 'Active', joinDate: '2024-01-14', location: 'Mogadishu', lastActive: '2024-01-19' },
          { id: 3, name: 'Fatima Ali', email: '<EMAIL>', type: 'Donor', bloodType: 'B+', status: 'Inactive', joinDate: '2024-01-13', location: 'Hargeisa', lastActive: '2024-01-15' },
          { id: 4, name: 'Mohamed Omar', email: '<EMAIL>', type: 'Donor', bloodType: 'AB-', status: 'Banned', joinDate: '2024-01-12', location: 'Bosaso', lastActive: '2024-01-14' },
          { id: 5, name: 'City General Hospital', email: '<EMAIL>', type: 'Hospital', location: 'New York', status: 'Verified', joinDate: '2024-01-10', lastActive: '2024-01-20' }
        ]);

        setPendingHospitals([
          { id: 1, name: 'Kalkaal Hospital', email: '<EMAIL>', location: 'Mogadishu, Somalia', requestDate: '2024-01-18', documents: 'Complete', phone: '+252 61 123 4567' },
          { id: 2, name: 'Medina Hospital', email: '<EMAIL>', location: 'Hargeisa, Somalia', requestDate: '2024-01-17', documents: 'Pending', phone: '+252 63 234 5678' },
          { id: 3, name: 'Bosaso Medical Center', email: '<EMAIL>', location: 'Bosaso, Somalia', requestDate: '2024-01-16', documents: 'Complete', phone: '+252 90 345 6789' }
        ]);

        setAllRequests([
          { id: 1, hospitalName: 'City General Hospital', bloodType: 'O+', units: 3, urgency: 'high', status: 'pending', datePosted: '2024-01-20', location: 'New York' },
          { id: 2, hospitalName: 'Kalkaal Hospital', bloodType: 'A-', units: 2, urgency: 'medium', status: 'pending', datePosted: '2024-01-19', location: 'Mogadishu' },
          { id: 3, hospitalName: 'Medina Hospital', bloodType: 'B+', units: 1, urgency: 'low', status: 'fulfilled', datePosted: '2024-01-18', location: 'Hargeisa' },
          { id: 4, hospitalName: 'Metro Medical', bloodType: 'AB-', units: 4, urgency: 'high', status: 'pending', datePosted: '2024-01-17', location: 'Brooklyn' }
        ]);

        setAnalytics({
          bloodTypeStats: [
            { bloodType: 'O+', requests: 45, donors: 890, shortage: false },
            { bloodType: 'O-', requests: 23, donors: 156, shortage: true },
            { bloodType: 'A+', requests: 34, donors: 567, shortage: false },
            { bloodType: 'A-', requests: 18, donors: 234, shortage: true },
            { bloodType: 'B+', requests: 28, donors: 445, shortage: false },
            { bloodType: 'B-', requests: 12, donors: 123, shortage: true },
            { bloodType: 'AB+', requests: 15, donors: 234, shortage: false },
            { bloodType: 'AB-', requests: 8, donors: 89, shortage: true }
          ],
          monthlyDonations: [
            { month: 'Jan', donations: 234 },
            { month: 'Feb', donations: 267 },
            { month: 'Mar', donations: 298 },
            { month: 'Apr', donations: 245 },
            { month: 'May', donations: 312 },
            { month: 'Jun', donations: 289 }
          ],
          regionalData: [
            { region: 'Mogadishu', donors: 1245, requests: 67, shortage: 'O-' },
            { region: 'Hargeisa', donors: 567, requests: 34, shortage: 'A-' },
            { region: 'Bosaso', donors: 234, requests: 12, shortage: 'B-' },
            { region: 'Kismayo', donors: 189, requests: 8, shortage: 'AB-' }
          ],
          urgentRequests: 5
        });

      } catch (error) {
        console.error('Error fetching admin data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchAdminData();
  }, []);

  // Filter users based on search and role
  const filteredUsers = allUsers.filter(user => {
    const matchesSearch = user.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         user.email.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesRole = filterRole === 'all' || user.type.toLowerCase() === filterRole.toLowerCase();
    return matchesSearch && matchesRole;
  });

  // Admin action handlers
  const handleVerifyHospital = async (hospitalId) => {
    try {
      // TODO: API call to verify hospital
      // await axios.patch(`/api/admin/verify-hospital/${hospitalId}`);

      setPendingHospitals(prev => prev.filter(h => h.id !== hospitalId));
      alert('Hospital verified successfully!');
    } catch (error) {
      console.error('Error verifying hospital:', error);
    }
  };

  const handleRejectHospital = async (hospitalId) => {
    try {
      // TODO: API call to reject hospital
      setPendingHospitals(prev => prev.filter(h => h.id !== hospitalId));
      alert('Hospital application rejected.');
    } catch (error) {
      console.error('Error rejecting hospital:', error);
    }
  };

  const handleBanUser = async (userId) => {
    try {
      // TODO: API call to ban user
      setAllUsers(prev => prev.map(user =>
        user.id === userId ? { ...user, status: 'Banned' } : user
      ));
      alert('User banned successfully!');
    } catch (error) {
      console.error('Error banning user:', error);
    }
  };

  const handleDeleteUser = async (userId) => {
    if (window.confirm('Are you sure you want to delete this user? This action cannot be undone.')) {
      try {
        // TODO: API call to delete user
        setAllUsers(prev => prev.filter(user => user.id !== userId));
        alert('User deleted successfully!');
      } catch (error) {
        console.error('Error deleting user:', error);
      }
    }
  };

  const handleSendGlobalAlert = () => {
    // TODO: Implement global alert functionality
    alert('Global alert feature coming soon!');
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p className="text-gray-600">Loading admin dashboard...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center space-x-3 mb-4">
            <Shield className="h-8 w-8 text-blue-600" />
            <h1 className="text-3xl font-bold text-gray-800">
              Admin Control Center 🛡️
            </h1>
          </div>
          <p className="text-gray-600 text-lg">
            Complete system oversight and management for 🩸 SomDonate
          </p>
        </div>

        {/* System Stats Grid */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-6 gap-6 mb-8">
          <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Users</p>
                <p className="text-2xl font-bold text-gray-800">{systemStats.totalUsers.toLocaleString()}</p>
                <p className="text-sm text-blue-600">+12% this month</p>
              </div>
              <Users className="h-8 w-8 text-blue-600" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Donors</p>
                <p className="text-2xl font-bold text-gray-800">{systemStats.totalDonors.toLocaleString()}</p>
                <p className="text-sm text-green-600">+8% this month</p>
              </div>
              <Heart className="h-8 w-8 text-red-500" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Hospitals</p>
                <p className="text-2xl font-bold text-gray-800">{systemStats.totalHospitals}</p>
                <p className="text-sm text-green-600">+3 verified</p>
              </div>
              <Building className="h-8 w-8 text-green-500" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Active Requests</p>
                <p className="text-2xl font-bold text-gray-800">{systemStats.activeRequests}</p>
                <p className="text-sm text-red-600">{analytics.urgentRequests} urgent</p>
              </div>
              <Activity className="h-8 w-8 text-purple-500" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Total Donations</p>
                <p className="text-2xl font-bold text-gray-800">{systemStats.totalDonations.toLocaleString()}</p>
                <p className="text-sm text-green-600">Lives saved</p>
              </div>
              <Target className="h-8 w-8 text-yellow-500" />
            </div>
          </div>

          <div className="bg-white p-6 rounded-xl shadow-lg hover:shadow-xl transition-shadow">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600">Pending Verifications</p>
                <p className="text-2xl font-bold text-gray-800">{systemStats.pendingVerifications}</p>
                <p className="text-sm text-yellow-600">Need attention</p>
              </div>
              <AlertCircle className="h-8 w-8 text-orange-500" />
            </div>
          </div>
        </div>

        {/* Enhanced Tabs */}
        <div className="bg-white rounded-lg shadow-sm mb-6">
          <div className="border-b border-gray-200">
            <nav className="flex space-x-8 px-6">
              {[
                { id: 'overview', label: 'Overview', icon: Database },
                { id: 'users', label: 'User Management', icon: Users },
                { id: 'hospitals', label: 'Hospital Verification', icon: Building },
                { id: 'requests', label: 'Blood Requests', icon: Activity },
                { id: 'analytics', label: 'Analytics', icon: BarChart3 },
                { id: 'alerts', label: 'Global Alerts', icon: Bell }
              ].map((tab) => (
                <button
                  key={tab.id}
                  onClick={() => setActiveTab(tab.id)}
                  className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center space-x-2 ${
                    activeTab === tab.id
                      ? 'border-blue-500 text-blue-600'
                      : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
                  }`}
                >
                  <tab.icon className="h-4 w-4" />
                  <span>{tab.label}</span>
                </button>
              ))}
            </nav>
          </div>
        </div>

        {/* Tab Content */}
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* System Health */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2">
                <Zap className="h-5 w-5" />
                <span>System Health</span>
              </h2>
              <div className="space-y-4">
                <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                  <span className="text-gray-700">Database Status</span>
                  <span className="text-green-600 font-semibold flex items-center space-x-1">
                    <CheckCircle className="h-4 w-4" />
                    <span>Online</span>
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-green-50 rounded-lg">
                  <span className="text-gray-700">API Response Time</span>
                  <span className="text-green-600 font-semibold">45ms</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-yellow-50 rounded-lg">
                  <span className="text-gray-700">Pending Verifications</span>
                  <span className="text-yellow-600 font-semibold">{systemStats.pendingVerifications}</span>
                </div>
                <div className="flex justify-between items-center p-3 bg-red-50 rounded-lg">
                  <span className="text-gray-700">Urgent Requests</span>
                  <span className="text-red-600 font-semibold">{analytics.urgentRequests}</span>
                </div>
              </div>
            </div>

            {/* Recent Activity */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2">
                <Activity className="h-5 w-5" />
                <span>Recent System Activity</span>
              </h2>
              <div className="space-y-4">
                <div className="flex items-start space-x-3 p-3 border-l-4 border-green-400 bg-green-50 rounded">
                  <CheckCircle className="h-5 w-5 text-green-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-gray-800">Hospital Verified</p>
                    <p className="text-xs text-gray-600">Kalkaal Hospital approved - 2 hours ago</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3 p-3 border-l-4 border-blue-400 bg-blue-50 rounded">
                  <Users className="h-5 w-5 text-blue-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-gray-800">New Donor Registration</p>
                    <p className="text-xs text-gray-600">Ahmed Hassan (O+) joined - 4 hours ago</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3 p-3 border-l-4 border-red-400 bg-red-50 rounded">
                  <AlertCircle className="h-5 w-5 text-red-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-gray-800">Urgent Blood Request</p>
                    <p className="text-xs text-gray-600">O- needed at Metro Medical - 6 hours ago</p>
                  </div>
                </div>
                <div className="flex items-start space-x-3 p-3 border-l-4 border-yellow-400 bg-yellow-50 rounded">
                  <Flag className="h-5 w-5 text-yellow-500 mt-0.5" />
                  <div>
                    <p className="text-sm font-medium text-gray-800">Suspicious Activity</p>
                    <p className="text-xs text-gray-600">Multiple failed login attempts - 8 hours ago</p>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Actions */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2">
                <Settings className="h-5 w-5" />
                <span>Quick Actions</span>
              </h2>
              <div className="grid grid-cols-2 gap-3">
                <button
                  onClick={handleSendGlobalAlert}
                  className="p-4 border-2 border-red-200 rounded-lg hover:border-red-400 hover:bg-red-50 transition-colors text-left"
                >
                  <Bell className="h-6 w-6 text-red-500 mb-2" />
                  <h3 className="font-semibold text-gray-800">Send Global Alert</h3>
                  <p className="text-sm text-gray-600">Emergency broadcast</p>
                </button>
                <button className="p-4 border-2 border-blue-200 rounded-lg hover:border-blue-400 hover:bg-blue-50 transition-colors text-left">
                  <Users className="h-6 w-6 text-blue-500 mb-2" />
                  <h3 className="font-semibold text-gray-800">Verify Hospitals</h3>
                  <p className="text-sm text-gray-600">{systemStats.pendingVerifications} pending</p>
                </button>
                <button className="p-4 border-2 border-green-200 rounded-lg hover:border-green-400 hover:bg-green-50 transition-colors text-left">
                  <BarChart3 className="h-6 w-6 text-green-500 mb-2" />
                  <h3 className="font-semibold text-gray-800">View Analytics</h3>
                  <p className="text-sm text-gray-600">System insights</p>
                </button>
                <button className="p-4 border-2 border-purple-200 rounded-lg hover:border-purple-400 hover:bg-purple-50 transition-colors text-left">
                  <Database className="h-6 w-6 text-purple-500 mb-2" />
                  <h3 className="font-semibold text-gray-800">System Backup</h3>
                  <p className="text-sm text-gray-600">Data protection</p>
                </button>
              </div>
            </div>

            {/* Regional Overview */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2">
                <Globe className="h-5 w-5" />
                <span>Regional Overview</span>
              </h2>
              <div className="space-y-3">
                {analytics.regionalData.map((region, index) => (
                  <div key={index} className="flex justify-between items-center p-3 border border-gray-200 rounded-lg">
                    <div>
                      <p className="font-semibold text-gray-800">{region.region}</p>
                      <p className="text-sm text-gray-600">{region.donors} donors • {region.requests} requests</p>
                    </div>
                    <div className="text-right">
                      <p className="text-sm text-red-600">Shortage: {region.shortage}</p>
                      <p className="text-xs text-gray-500">Critical</p>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'users' && (
          <div className="bg-white rounded-xl shadow-lg p-6">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-bold text-gray-800 flex items-center space-x-2">
                <Users className="h-5 w-5" />
                <span>User Management</span>
              </h2>
              <div className="flex space-x-3">
                <select
                  value={filterRole}
                  onChange={(e) => setFilterRole(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg"
                >
                  <option value="all">All Users</option>
                  <option value="donor">Donors</option>
                  <option value="hospital">Hospitals</option>
                </select>
                <div className="relative">
                  <Search className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search users..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10 pr-3 py-2 border border-gray-300 rounded-lg"
                  />
                </div>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">User</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Type</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Blood Type</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Location</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Status</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Last Active</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredUsers.map((user) => (
                    <tr key={user.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div>
                          <p className="font-semibold text-gray-800">{user.name}</p>
                          <p className="text-sm text-gray-600">{user.email}</p>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          user.type === 'Donor' ? 'bg-red-100 text-red-800' : 'bg-blue-100 text-blue-800'
                        }`}>
                          {user.type}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        {user.bloodType ? (
                          <span className="font-semibold text-red-600">{user.bloodType}</span>
                        ) : (
                          <span className="text-gray-400">N/A</span>
                        )}
                      </td>
                      <td className="py-3 px-4 text-sm text-gray-600">{user.location}</td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          user.status === 'Active' ? 'bg-green-100 text-green-800' :
                          user.status === 'Verified' ? 'bg-blue-100 text-blue-800' :
                          user.status === 'Banned' ? 'bg-red-100 text-red-800' :
                          'bg-gray-100 text-gray-800'
                        }`}>
                          {user.status}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-sm text-gray-600">
                        {new Date(user.lastActive).toLocaleDateString()}
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex space-x-2">
                          <button className="text-blue-600 hover:text-blue-800 text-sm">
                            <Eye className="h-4 w-4" />
                          </button>
                          {user.status !== 'Banned' && (
                            <button
                              onClick={() => handleBanUser(user.id)}
                              className="text-yellow-600 hover:text-yellow-800 text-sm"
                            >
                              <Ban className="h-4 w-4" />
                            </button>
                          )}
                          <button
                            onClick={() => handleDeleteUser(user.id)}
                            className="text-red-600 hover:text-red-800 text-sm"
                          >
                            <Trash2 className="h-4 w-4" />
                          </button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {filteredUsers.length === 0 && (
              <div className="text-center py-8">
                <Users className="h-12 w-12 text-gray-300 mx-auto mb-4" />
                <p className="text-gray-600">No users found matching your criteria.</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'hospitals' && (
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2">
              <Building className="h-5 w-5" />
              <span>Hospital Verification Center</span>
            </h2>

            {pendingHospitals.length > 0 ? (
              <div className="space-y-6">
                <div className="bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6">
                  <div className="flex items-center space-x-2">
                    <AlertCircle className="h-5 w-5 text-yellow-600" />
                    <p className="text-yellow-800 font-medium">
                      {pendingHospitals.length} hospitals awaiting verification
                    </p>
                  </div>
                </div>

                {pendingHospitals.map((hospital) => (
                  <div key={hospital.id} className="border border-gray-200 rounded-lg p-6">
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                      <div>
                        <h3 className="text-lg font-bold text-gray-800 mb-3">{hospital.name}</h3>
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <Mail className="h-4 w-4 text-gray-500" />
                            <span className="text-gray-700">{hospital.email}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <MapPin className="h-4 w-4 text-gray-500" />
                            <span className="text-gray-700">{hospital.location}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Phone className="h-4 w-4 text-gray-500" />
                            <span className="text-gray-700">{hospital.phone}</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Calendar className="h-4 w-4 text-gray-500" />
                            <span className="text-gray-700">Applied: {new Date(hospital.requestDate).toLocaleDateString()}</span>
                          </div>
                        </div>
                      </div>

                      <div>
                        <h4 className="font-semibold text-gray-800 mb-3">Verification Status</h4>
                        <div className="space-y-2">
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600">Documents</span>
                            <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                              hospital.documents === 'Complete'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-yellow-100 text-yellow-800'
                            }`}>
                              {hospital.documents}
                            </span>
                          </div>
                          <div className="flex justify-between items-center">
                            <span className="text-gray-600">Background Check</span>
                            <span className="px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                              In Progress
                            </span>
                          </div>
                        </div>

                        <div className="flex space-x-3 mt-6">
                          <button
                            onClick={() => handleVerifyHospital(hospital.id)}
                            className="flex-1 bg-green-600 text-white px-4 py-2 rounded-lg hover:bg-green-700 transition-colors font-medium flex items-center justify-center space-x-2"
                          >
                            <CheckCircle className="h-4 w-4" />
                            <span>Approve</span>
                          </button>
                          <button
                            onClick={() => handleRejectHospital(hospital.id)}
                            className="flex-1 bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 transition-colors font-medium flex items-center justify-center space-x-2"
                          >
                            <AlertCircle className="h-4 w-4" />
                            <span>Reject</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <div className="text-center py-12">
                <Building className="h-16 w-16 text-gray-300 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-800 mb-2">All Caught Up!</h3>
                <p className="text-gray-600">No hospitals pending verification at the moment.</p>
              </div>
            )}
          </div>
        )}

        {activeTab === 'requests' && (
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2">
              <Activity className="h-5 w-5" />
              <span>All Blood Requests</span>
            </h2>

            <div className="mb-6">
              <div className="flex space-x-4">
                <select className="px-3 py-2 border border-gray-300 rounded-lg">
                  <option>All Status</option>
                  <option>Pending</option>
                  <option>Fulfilled</option>
                </select>
                <select className="px-3 py-2 border border-gray-300 rounded-lg">
                  <option>All Urgency</option>
                  <option>High</option>
                  <option>Medium</option>
                  <option>Low</option>
                </select>
                <div className="relative">
                  <Search className="h-4 w-4 absolute left-3 top-3 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search requests..."
                    className="pl-10 pr-3 py-2 border border-gray-300 rounded-lg"
                  />
                </div>
              </div>
            </div>

            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b border-gray-200">
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Hospital</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Blood Type</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Units</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Urgency</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Status</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Date</th>
                    <th className="text-left py-3 px-4 font-semibold text-gray-700">Location</th>
                  </tr>
                </thead>
                <tbody>
                  {allRequests.map((request) => (
                    <tr key={request.id} className="border-b border-gray-100 hover:bg-gray-50">
                      <td className="py-3 px-4 font-semibold text-gray-800">{request.hospitalName}</td>
                      <td className="py-3 px-4">
                        <span className="font-bold text-red-600">{request.bloodType}</span>
                      </td>
                      <td className="py-3 px-4">{request.units}</td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          request.urgency === 'high' ? 'bg-red-100 text-red-800' :
                          request.urgency === 'medium' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {request.urgency}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                          request.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                          'bg-green-100 text-green-800'
                        }`}>
                          {request.status}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-sm text-gray-600">
                        {new Date(request.datePosted).toLocaleDateString()}
                      </td>
                      <td className="py-3 px-4 text-sm text-gray-600">{request.location}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        )}

        {activeTab === 'analytics' && (
          <div className="space-y-8">
            {/* Blood Type Analytics */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2">
                <BarChart3 className="h-5 w-5" />
                <span>Blood Type Analytics</span>
              </h2>

              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                {analytics.bloodTypeStats.map((stat, index) => (
                  <div key={index} className={`p-4 rounded-lg border-2 ${
                    stat.shortage ? 'border-red-200 bg-red-50' : 'border-green-200 bg-green-50'
                  }`}>
                    <div className="flex justify-between items-center mb-2">
                      <span className="text-2xl font-bold text-gray-800">{stat.bloodType}</span>
                      {stat.shortage && <AlertCircle className="h-5 w-5 text-red-500" />}
                    </div>
                    <div className="space-y-1">
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Requests:</span>
                        <span className="font-semibold">{stat.requests}</span>
                      </div>
                      <div className="flex justify-between text-sm">
                        <span className="text-gray-600">Donors:</span>
                        <span className="font-semibold">{stat.donors}</span>
                      </div>
                      <div className="text-xs mt-2">
                        {stat.shortage ? (
                          <span className="text-red-600 font-medium">⚠️ Critical Shortage</span>
                        ) : (
                          <span className="text-green-600 font-medium">✅ Adequate Supply</span>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Monthly Trends */}
            <div className="bg-white rounded-xl shadow-lg p-6">
              <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2">
                <TrendingUp className="h-5 w-5" />
                <span>Monthly Donation Trends</span>
              </h2>

              <div className="grid grid-cols-6 gap-4">
                {analytics.monthlyDonations.map((month, index) => (
                  <div key={index} className="text-center">
                    <div className="bg-blue-100 rounded-lg p-4 mb-2">
                      <div className="text-2xl font-bold text-blue-600">{month.donations}</div>
                    </div>
                    <div className="text-sm text-gray-600">{month.month}</div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}

        {activeTab === 'alerts' && (
          <div className="bg-white rounded-xl shadow-lg p-6">
            <h2 className="text-xl font-bold text-gray-800 mb-6 flex items-center space-x-2">
              <Bell className="h-5 w-5" />
              <span>Global Alert System</span>
            </h2>

            <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Send Emergency Alert</h3>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Blood Type</label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-lg">
                      <option>Select Blood Type</option>
                      {['O+', 'O-', 'A+', 'A-', 'B+', 'B-', 'AB+', 'AB-'].map(type => (
                        <option key={type} value={type}>{type}</option>
                      ))}
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Region</label>
                    <select className="w-full px-3 py-2 border border-gray-300 rounded-lg">
                      <option>All Regions</option>
                      <option>Mogadishu</option>
                      <option>Hargeisa</option>
                      <option>Bosaso</option>
                      <option>Kismayo</option>
                    </select>
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Message</label>
                    <textarea
                      rows={4}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg"
                      placeholder="Enter emergency message..."
                    />
                  </div>
                  <button
                    onClick={handleSendGlobalAlert}
                    className="w-full bg-red-600 text-white px-4 py-3 rounded-lg hover:bg-red-700 transition-colors font-medium flex items-center justify-center space-x-2"
                  >
                    <Send className="h-5 w-5" />
                    <span>Send Global Alert</span>
                  </button>
                </div>
              </div>

              <div>
                <h3 className="text-lg font-semibold text-gray-800 mb-4">Recent Alerts</h3>
                <div className="space-y-3">
                  <div className="p-4 border border-red-200 bg-red-50 rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <span className="font-semibold text-red-800">Emergency: O- Blood Needed</span>
                      <span className="text-xs text-red-600">2 hours ago</span>
                    </div>
                    <p className="text-sm text-red-700">Critical shortage at Metro Medical Center. 3 patients in emergency surgery.</p>
                    <p className="text-xs text-red-600 mt-2">Sent to 156 O- donors in New York area</p>
                  </div>

                  <div className="p-4 border border-yellow-200 bg-yellow-50 rounded-lg">
                    <div className="flex justify-between items-start mb-2">
                      <span className="font-semibold text-yellow-800">Alert: A- Blood Low</span>
                      <span className="text-xs text-yellow-600">1 day ago</span>
                    </div>
                    <p className="text-sm text-yellow-700">Running low on A- blood type across all hospitals.</p>
                    <p className="text-xs text-yellow-600 mt-2">Sent to 234 A- donors nationwide</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default AdminDashboard;
