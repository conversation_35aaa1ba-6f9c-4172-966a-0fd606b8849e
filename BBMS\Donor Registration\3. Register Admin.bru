meta {
  name: 3. Register Admin
  type: http
  seq: 3
}

post {
  url: http://localhost:3001/api/auth/register-donor
  body: json
  auth: inherit
}

body:json {
  {
    "name": "Admin User",
    "email": "<EMAIL>",
    "password": "admin123",
    "bloodType": "AB+",
    "location": "Admin Office",
    "phone": "555-0001"
  }
}

tests {
  test("Should register admin successfully", function() {
    expect(res.getStatus()).to.equal(201);
    expect(res.getBody().success).to.equal(true);
    expect(res.getBody().token).to.be.a('string');
  });
}

docs {
  Note: Admin users are created as regular users first, then their role needs to be manually updated in the database to 'admin'
}
